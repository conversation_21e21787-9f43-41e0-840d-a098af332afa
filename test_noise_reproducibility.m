% 测试噪声对定位结果重现性的影响
function test_noise_reproducibility()
clear; clc;

fprintf('=== 测试噪声对定位结果重现性的影响 ===\n\n');

% 创建简单的测试环境
SpeedImage = create_test_model();
stations = [50, 50; 150, 50; 150, 150; 50, 150];
test_source = [100, 100];

% 计算走时表
fprintf('计算走时表...\n');
travel_time_tables = cell(4, 1);
for i = 1:4
    travel_time_tables{i} = compute_simple_travel_times(SpeedImage, stations(i,:)');
end

% ===== 测试1: 固定噪声模式 =====
fprintf('\n=== 测试1: 固定噪声模式 (结果应该相同) ===\n');

errors_fixed = zeros(5, 1);
for run = 1:5
    fprintf('运行 %d: ', run);
    
    % 使用固定噪声
    observed_times = generate_test_observations(test_source, travel_time_tables, stations, ...
                                               'UseFixedNoise', true, 'NoiseLevel', 0.003);
    
    estimated_source = simple_grid_search(observed_times, travel_time_tables);
    error = norm(test_source - estimated_source);
    errors_fixed(run) = error;
    
    fprintf('误差 = %.3f, 定位 = [%d, %d]\n', error, estimated_source(1), estimated_source(2));
end

fprintf('固定噪声模式统计:\n');
fprintf('  误差范围: %.3f - %.3f\n', min(errors_fixed), max(errors_fixed));
fprintf('  误差标准差: %.6f (应该为0或很小)\n', std(errors_fixed));

% ===== 测试2: 随机噪声模式 =====
fprintf('\n=== 测试2: 随机噪声模式 (结果应该不同) ===\n');

errors_random = zeros(5, 1);
for run = 1:5
    fprintf('运行 %d: ', run);
    
    % 使用随机噪声
    observed_times = generate_test_observations(test_source, travel_time_tables, stations, ...
                                               'UseFixedNoise', false, 'NoiseLevel', 0.003);
    
    estimated_source = simple_grid_search(observed_times, travel_time_tables);
    error = norm(test_source - estimated_source);
    errors_random(run) = error;
    
    fprintf('误差 = %.3f, 定位 = [%d, %d]\n', error, estimated_source(1), estimated_source(2));
end

fprintf('随机噪声模式统计:\n');
fprintf('  误差范围: %.3f - %.3f\n', min(errors_random), max(errors_random));
fprintf('  误差标准差: %.6f (应该 > 0)\n', std(errors_random));

% ===== 测试3: 无噪声模式 =====
fprintf('\n=== 测试3: 无噪声模式 (理论最佳结果) ===\n');

observed_times = generate_test_observations(test_source, travel_time_tables, stations, ...
                                           'NoiseLevel', 0);
estimated_source = simple_grid_search(observed_times, travel_time_tables);
error_no_noise = norm(test_source - estimated_source);

fprintf('无噪声误差: %.3f, 定位 = [%d, %d]\n', error_no_noise, estimated_source(1), estimated_source(2));

% ===== 结果分析 =====
fprintf('\n=== 结果分析 ===\n');

if std(errors_fixed) < 1e-10
    fprintf('✅ 固定噪声模式工作正常 - 结果完全一致\n');
elseif std(errors_fixed) < 0.1
    fprintf('✅ 固定噪声模式基本正常 - 结果基本一致\n');
else
    fprintf('❌ 固定噪声模式有问题 - 结果不一致\n');
end

if std(errors_random) > 0.1
    fprintf('✅ 随机噪声模式工作正常 - 结果有变化\n');
else
    fprintf('⚠️  随机噪声模式变化较小 - 可能噪声水平不够\n');
end

fprintf('\n推荐使用方式:\n');
fprintf('1. 算法测试和调试: 使用固定噪声模式\n');
fprintf('2. 性能评估: 使用随机噪声模式多次运行\n');
fprintf('3. 理论分析: 使用无噪声模式\n');

% 可视化结果
figure('Name', '噪声模式对比', 'Position', [100, 100, 800, 400]);

subplot(1, 2, 1);
plot(1:5, errors_fixed, 'bo-', 'LineWidth', 2, 'MarkerSize', 8, 'MarkerFaceColor', 'blue');
title('固定噪声模式');
xlabel('运行次数'); ylabel('定位误差');
grid on; ylim([0, max([errors_fixed; errors_random]) * 1.1]);

subplot(1, 2, 2);
plot(1:5, errors_random, 'ro-', 'LineWidth', 2, 'MarkerSize', 8, 'MarkerFaceColor', 'red');
title('随机噪声模式');
xlabel('运行次数'); ylabel('定位误差');
grid on; ylim([0, max([errors_fixed; errors_random]) * 1.1]);

sgtitle('不同噪声模式的定位误差对比');

fprintf('\n=== 测试完成 ===\n');
end

function SpeedImage = create_test_model()
% 创建简单的测试速度模型
SpeedImage = ones(200, 200) * 3000; % 均匀速度3000 m/s

% 添加一个简单的速度异常
SpeedImage(80:120, 80:120) = 4000; % 高速区域
end

function T = compute_simple_travel_times(SpeedImage, source_pos)
% 简单的走时计算
[rows, cols] = size(SpeedImage);
T = zeros(rows, cols);

source_x = round(source_pos(1));
source_y = round(source_pos(2));

for i = 1:rows
    for j = 1:cols
        % 使用直线距离和平均速度
        distance = sqrt((i - source_x)^2 + (j - source_y)^2);
        avg_velocity = mean([SpeedImage(source_x, source_y), SpeedImage(i, j)]);
        T(i, j) = distance / avg_velocity;
    end
end
end

function observed_times = generate_test_observations(true_source, travel_time_tables, stations, varargin)
% 生成测试观测数据
p = inputParser;
addParameter(p, 'NoiseLevel', 0.002, @isnumeric);
addParameter(p, 'UseFixedNoise', true, @islogical);
parse(p, varargin{:});

noise_level = p.Results.NoiseLevel;
use_fixed_noise = p.Results.UseFixedNoise;

num_stations = length(travel_time_tables);
observed_times = zeros(num_stations, 1);

for i = 1:num_stations
    % 提取理论走时
    x = true_source(1);
    y = true_source(2);
    observed_times(i) = travel_time_tables{i}(x, y);
    
    % 添加噪声
    if noise_level > 0
        if use_fixed_noise
            % 固定噪声：基于位置和台站的确定性噪声
            noise_seed = x * 1000 + y * 100 + i * 10;
            rng(noise_seed, 'twister');
            noise = noise_level * randn();
            rng('shuffle');
        else
            % 随机噪声
            noise = noise_level * randn();
        end
        
        observed_times(i) = observed_times(i) + noise;
    end
    
    observed_times(i) = max(observed_times(i), 1e-6);
end
end

function best_location = simple_grid_search(observed_times, travel_time_tables)
% 简单的网格搜索
search_range = 20:4:180;
min_residual = inf;
best_location = [100, 100];

for i = 1:length(search_range)
    for j = 1:length(search_range)
        x = search_range(i);
        y = search_range(j);
        
        % 计算理论走时
        theoretical_times = zeros(length(travel_time_tables), 1);
        for k = 1:length(travel_time_tables)
            theoretical_times(k) = travel_time_tables{k}(x, y);
        end
        
        % 计算残差
        residual = sqrt(mean((observed_times - theoretical_times).^2));
        
        if residual < min_residual
            min_residual = residual;
            best_location = [x, y];
        end
    end
end
end
