# Runge-Kutta射线追踪法地震定位系统

本项目提供了基于Runge-Kutta射线追踪法的地震定位系统，可以替代MSFM2D算法实现相同的功能。

## 文件说明

### 核心文件

1. **rk_raytracing.c** - C语言实现的RK射线追踪MEX函数
   - 实现四阶Runge-Kutta积分器
   - 求解射线方程组
   - 计算地震波传播走时

2. **rk_raytracing2d.m** - MATLAB包装函数
   - 提供与msfm2d相同的接口
   - 包含MATLAB备用实现
   - 自动编译和调用MEX函数

3. **analyze_location_errors_rk.m** - RK射线追踪误差分析
   - 多点定位精度测试
   - 统计误差分析
   - 可视化结果

4. **compare_msfm_vs_rk.m** - 性能比较脚本
   - MSFM2D与RK射线追踪对比
   - 时间和精度性能分析
   - 详细比较可视化

## 算法原理

### Runge-Kutta射线追踪法

射线追踪基于求解射线方程组：

```
dx/dt = v² * px
dy/dt = v² * py
dpx/dt = -∂v/∂x / v³
dpy/dt = -∂v/∂y / v³
dt/dt = 1/v
```

其中：
- (x, y) 是射线位置
- (px, py) 是慢度分量
- v(x,y) 是速度场
- t 是走时

### 四阶Runge-Kutta积分

使用经典的RK4方法数值求解：

```
k1 = f(t, y)
k2 = f(t + h/2, y + h*k1/2)
k3 = f(t + h/2, y + h*k2/2)
k4 = f(t + h, y + h*k3)
y_{n+1} = y_n + h/6 * (k1 + 2*k2 + 2*k3 + k4)
```

## 使用方法

### 1. 编译MEX文件

```matlab
% 在MATLAB中编译C代码
mex rk_raytracing.c -output rk_raytracing_mex
```

### 2. 基本使用

```matlab
% 创建速度模型
SpeedImage = ones(100, 100) * 3000; % 均匀速度3000 m/s

% 设置震源位置
source_pos = [50, 50];

% 计算走时表
T = rk_raytracing2d(SpeedImage, source_pos');

% 显示结果
figure;
pcolor(T); shading interp; colorbar;
title('走时表');
```

### 3. 地震定位误差分析

```matlab
% 运行完整的误差分析
analyze_location_errors_rk();
```

### 4. 性能比较

```matlab
% 比较MSFM2D和RK射线追踪
compare_msfm_vs_rk();
```

## 主要特点

### 优势

1. **高精度**: 四阶RK方法提供高精度数值解
2. **稳定性**: 自适应步长控制确保数值稳定
3. **灵活性**: 可处理任意复杂的速度模型
4. **兼容性**: 与msfm2d接口完全兼容

### 适用场景

- 复杂地下速度结构建模
- 高精度地震定位
- 射线路径分析
- 地震波传播模拟

## 参数设置

### 射线追踪参数

```c
#define MAX_STEPS 10000    // 最大积分步数
#define STEP_SIZE 0.5      // 积分步长
#define TOLERANCE 1e-6     // 收敛容差
```

### 多角度追踪

```c
int num_angles = 8;        // 射线角度数量
double angle_range = PI/6; // 角度搜索范围 (±30度)
```

## 性能对比

| 指标 | MSFM2D | RK射线追踪 |
|------|--------|------------|
| 计算精度 | 高 | 很高 |
| 计算速度 | 快 | 中等 |
| 内存使用 | 中等 | 低 |
| 复杂模型适应性 | 好 | 很好 |

## 故障排除

### 常见问题

1. **MEX编译失败**
   - 检查编译器配置: `mex -setup`
   - 确保有C编译器

2. **计算速度慢**
   - 减少MAX_STEPS
   - 增大STEP_SIZE
   - 使用简化模型

3. **精度不足**
   - 减小STEP_SIZE
   - 增加num_angles
   - 检查速度模型插值

### 调试建议

```matlab
% 启用详细输出
rk_raytracing2d(SpeedImage, source_pos', 'verbose', true);

% 检查走时表
figure; pcolor(T); shading interp; colorbar;

% 验证震源点走时
fprintf('震源点走时: %.6f\n', T(source_pos(1), source_pos(2)));
```

## 扩展功能

### 3D射线追踪

可以扩展到三维：

```matlab
% 3D版本接口设计
T3D = rk_raytracing3d(SpeedImage3D, source_pos');
```

### 各向异性介质

支持各向异性速度模型：

```matlab
% 各向异性参数
T_aniso = rk_raytracing2d(SpeedImage, source_pos', ...
                         'anisotropy', aniso_params);
```

## 参考文献

1. Červený, V. (2001). Seismic Ray Theory. Cambridge University Press.
2. Virieux, J., & Farra, V. (1991). Ray tracing in 3-D complex isotropic media. Geophysics, 56(12), 2057-2069.
3. Hairer, E., Nørsett, S. P., & Wanner, G. (1993). Solving Ordinary Differential Equations I. Springer.

## 版本历史

- v1.0: 基本RK射线追踪实现
- v1.1: 添加多角度追踪
- v1.2: 性能优化和错误处理
- v1.3: 完整的误差分析系统

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 本实现仅用于科研和教学目的，商业使用请联系开发者。
