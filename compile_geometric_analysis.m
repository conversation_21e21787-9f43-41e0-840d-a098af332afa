%% 编译几何分析法MEX文件
% 这个脚本用于编译geometric_analysis_3d.c文件

fprintf('正在编译几何分析法MEX文件...\n');

try
    % 编译MEX文件
    mex geometric_analysis_3d.c
    fprintf('编译成功！\n');
    
    % 测试编译结果
    fprintf('测试编译结果...\n');
    
    % 创建简单测试数据
    test_speed = ones([10, 10, 10]) * 1500;
    test_source = [5; 5; 5];
    
    % 测试几何分析法
    result = geometric_analysis_3d(test_speed, test_source, 0);
    
    if ~isempty(result) && all(size(result) == [10, 10, 10])
        fprintf('测试通过！几何分析法MEX文件可以正常使用。\n');
    else
        fprintf('测试失败！输出结果不正确。\n');
    end
    
catch ME
    fprintf('编译失败: %s\n', ME.message);
    fprintf('\n可能的解决方案:\n');
    fprintf('1. 确保已安装MATLAB编译器 (mex -setup)\n');
    fprintf('2. 检查geometric_analysis_3d.c文件是否存在\n');
    fprintf('3. 确保C编译器已正确配置\n');
end
