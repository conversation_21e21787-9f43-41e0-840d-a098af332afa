%% 四种方法完整比较：MSFM3D vs 几何分析法 vs Dijkstra vs A*
% 全面比较四种3D传播时间计算方法

clear; clc;

fprintf('=== 四种方法完整比较测试 ===\n');
fprintf('1. MSFM3D (快速行进方法)\n');
fprintf('2. 几何分析法 (直接计算)\n');
fprintf('3. Dijkstra算法 (图论最短路径)\n');
fprintf('4. A*算法 (启发式搜索)\n\n');

%% 测试参数设置
SourcePoint = [1; 1; 1];
SpeedImage = ones([200 200 200]) * 1500;

% 创建理论解
[X, Y, Z] = ndgrid(1:200, 1:200, 1:200);
T_theory = (sqrt((X-SourcePoint(1)).^2 + (Y-SourcePoint(2)).^2 + (Z-SourcePoint(3)).^2)) ./ SpeedImage;

fprintf('测试配置:\n');
fprintf('- 网格尺寸: 200x200x200\n');
fprintf('- 源点位置: (%d, %d, %d)\n', SourcePoint(1), SourcePoint(2), SourcePoint(3));
fprintf('- 均匀速度: %.0f m/s\n', SpeedImage(1));
fprintf('- 总体素数: %d\n\n', numel(SpeedImage));

%% 方法1: 几何分析法
fprintf('=== 方法1: 几何分析法 ===\n');
tic;
try
    T_geometric = geometric_analysis_simple(SpeedImage, SourcePoint);
    time_geometric = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_geometric);
    error_geometric = calculate_errors(T_theory, T_geometric);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_geometric.L1, error_geometric.L2, error_geometric.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_geometric = []; time_geometric = NaN;
    error_geometric = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法2: A*算法 (标准配置)
fprintf('\n=== 方法2: A*算法 (标准) ===\n');
tic;
try
    T_astar_std = astar_3d_matlab(SpeedImage, SourcePoint, 6, 1.0);
    time_astar_std = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_astar_std);
    error_astar_std = calculate_errors(T_theory, T_astar_std);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_astar_std.L1, error_astar_std.L2, error_astar_std.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_astar_std = []; time_astar_std = NaN;
    error_astar_std = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法3: A*算法 (高级配置)
fprintf('\n=== 方法3: A*算法 (高级) ===\n');
options_advanced = struct();
options_advanced.connectivity = 26;
options_advanced.heuristic_type = 'adaptive';
options_advanced.heuristic_weight = 1.2;
options_advanced.tie_breaking = true;

tic;
try
    % 使用较小网格测试高级A*
    small_size = [100 100 100];
    SpeedImage_small = ones(small_size) * 1500;
    SourcePoint_small = [1; 1; 1];
    
    [X_s, Y_s, Z_s] = ndgrid(1:small_size(1), 1:small_size(2), 1:small_size(3));
    T_theory_small = (sqrt((X_s-SourcePoint_small(1)).^2 + (Y_s-SourcePoint_small(2)).^2 + (Z_s-SourcePoint_small(3)).^2)) ./ SpeedImage_small;
    
    T_astar_adv_small = astar_3d_advanced(SpeedImage_small, SourcePoint_small, options_advanced);
    time_astar_adv = toc;
    fprintf('✓ 成功 (100x100x100)，用时: %.4f 秒\n', time_astar_adv);
    error_astar_adv = calculate_errors(T_theory_small, T_astar_adv_small);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_astar_adv.L1, error_astar_adv.L2, error_astar_adv.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_astar_adv_small = []; time_astar_adv = NaN;
    error_astar_adv = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法4: Dijkstra算法
fprintf('\n=== 方法4: Dijkstra算法 ===\n');
tic;
try
    T_dijkstra = dijkstra_3d_matlab(SpeedImage, SourcePoint, 6);
    time_dijkstra = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_dijkstra);
    error_dijkstra = calculate_errors(T_theory, T_dijkstra);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dijkstra.L1, error_dijkstra.L2, error_dijkstra.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dijkstra = []; time_dijkstra = NaN;
    error_dijkstra = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法5-8: MSFM3D的四种配置
fprintf('\n=== 方法5-8: MSFM3D算法 ===\n');

methods_msfm = {'FMM1', 'MSFM1', 'FMM2', 'MSFM2'};
params_msfm = {[false, false], [false, true], [true, false], [true, true]};
results_msfm = cell(1, 4);
times_msfm = zeros(1, 4);
errors_msfm = cell(1, 4);

for i = 1:4
    fprintf('方法%d - %s: ', i+4, methods_msfm{i});
    tic;
    try
        results_msfm{i} = msfm3d(SpeedImage, SourcePoint, params_msfm{i}(1), params_msfm{i}(2));
        times_msfm(i) = toc;
        fprintf('✓ 用时: %.4f 秒\n', times_msfm(i));
        errors_msfm{i} = calculate_errors(T_theory, results_msfm{i});
    catch ME
        fprintf('✗ 失败: %s\n', ME.message);
        results_msfm{i} = [];
        times_msfm(i) = NaN;
        errors_msfm{i} = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
    end
end

%% 结果汇总表
fprintf('\n=== 完整结果汇总 ===\n');
fprintf('%-20s %12s %12s %12s %12s %12s\n', '方法', 'L1误差', 'L2误差', 'L∞误差', '时间(秒)', '效率');
fprintf('%s\n', repmat('-', 1, 85));

% 收集所有结果
all_methods = {};
all_errors = [];
all_times = [];
all_efficiency = [];

% 几何分析法
if ~isempty(T_geometric)
    all_methods{end+1} = '几何分析法';
    all_errors = [all_errors; error_geometric.L1, error_geometric.L2, error_geometric.Linf];
    all_times = [all_times; time_geometric];
    all_efficiency = [all_efficiency; numel(SpeedImage)/time_geometric];
end

% A*标准
if ~isempty(T_astar_std)
    all_methods{end+1} = 'A*标准';
    all_errors = [all_errors; error_astar_std.L1, error_astar_std.L2, error_astar_std.Linf];
    all_times = [all_times; time_astar_std];
    all_efficiency = [all_efficiency; numel(SpeedImage)/time_astar_std];
end

% A*高级 (注意：使用小网格)
if ~isempty(T_astar_adv_small)
    all_methods{end+1} = 'A*高级(小网格)';
    all_errors = [all_errors; error_astar_adv.L1, error_astar_adv.L2, error_astar_adv.Linf];
    all_times = [all_times; time_astar_adv];
    all_efficiency = [all_efficiency; numel(SpeedImage_small)/time_astar_adv];
end

% Dijkstra
if ~isempty(T_dijkstra)
    all_methods{end+1} = 'Dijkstra';
    all_errors = [all_errors; error_dijkstra.L1, error_dijkstra.L2, error_dijkstra.Linf];
    all_times = [all_times; time_dijkstra];
    all_efficiency = [all_efficiency; numel(SpeedImage)/time_dijkstra];
end

% MSFM方法
for i = 1:4
    if ~isempty(results_msfm{i})
        all_methods{end+1} = methods_msfm{i};
        all_errors = [all_errors; errors_msfm{i}.L1, errors_msfm{i}.L2, errors_msfm{i}.Linf];
        all_times = [all_times; times_msfm(i)];
        all_efficiency = [all_efficiency; numel(SpeedImage)/times_msfm(i)];
    end
end

% 显示结果表
for i = 1:length(all_methods)
    fprintf('%-20s %12.2e %12.2e %12.2e %12.4f %12.0f\n', ...
        all_methods{i}, all_errors(i,1), all_errors(i,2), all_errors(i,3), ...
        all_times(i), all_efficiency(i));
end

%% 性能分析
fprintf('\n=== 性能分析 ===\n');

% 精度排序
if ~isempty(all_errors)
    [~, precision_rank] = sort(all_errors(:,1));
    fprintf('精度排序 (按L1误差):\n');
    for i = 1:length(precision_rank)
        idx = precision_rank(i);
        fprintf('  %d. %s: L1=%.2e\n', i, all_methods{idx}, all_errors(idx,1));
    end
end

% 速度排序
if ~isempty(all_times)
    [~, speed_rank] = sort(all_times);
    fprintf('\n速度排序 (按计算时间):\n');
    for i = 1:length(speed_rank)
        idx = speed_rank(i);
        fprintf('  %d. %s: %.4f秒\n', i, all_methods{idx}, all_times(idx));
    end
end

% 效率排序
if ~isempty(all_efficiency)
    [~, efficiency_rank] = sort(all_efficiency, 'descend');
    fprintf('\n效率排序 (节点/秒):\n');
    for i = 1:length(efficiency_rank)
        idx = efficiency_rank(i);
        fprintf('  %d. %s: %.0f节点/秒\n', i, all_methods{idx}, all_efficiency(idx));
    end
end

%% 算法特性总结
fprintf('\n=== 算法特性总结 ===\n');
fprintf('1. 几何分析法:\n');
fprintf('   - 优点: 均匀介质中最精确最快\n');
fprintf('   - 缺点: 仅适用于简单介质\n');
fprintf('   - 适用: 均匀或简单分层介质\n\n');

fprintf('2. A*算法:\n');
fprintf('   - 优点: 启发式指导，通常比Dijkstra快\n');
fprintf('   - 缺点: 启发式设计影响性能\n');
fprintf('   - 适用: 需要平衡精度和速度的场景\n\n');

fprintf('3. Dijkstra算法:\n');
fprintf('   - 优点: 理论保证最短路径\n');
fprintf('   - 缺点: 计算量大，无启发式指导\n');
fprintf('   - 适用: 需要绝对精确结果的场景\n\n');

fprintf('4. MSFM算法:\n');
fprintf('   - 优点: 专为波传播设计，精度高\n');
fprintf('   - 缺点: 实现复杂，参数敏感\n');
fprintf('   - 适用: 复杂介质中的波传播模拟\n\n');

%% 应用建议
fprintf('=== 应用建议 ===\n');
fprintf('• 实时应用: 几何分析法 (最快)\n');
fprintf('• 高精度需求: MSFM2 或 Dijkstra-26\n');
fprintf('• 平衡性能: A*算法 (标准配置)\n');
fprintf('• 复杂介质: MSFM2 (最适合)\n');
fprintf('• 基准测试: Dijkstra (理论最优)\n');

%% 辅助函数
function errors = calculate_errors(reference, computed)
    diff = reference(:) - computed(:);
    errors.L1 = mean(abs(diff));
    errors.L2 = sqrt(mean(diff.^2));
    errors.Linf = max(abs(diff));
end
