% 基于Runge-Kutta射线追踪法的地震定位误差分析完整代码
function analyze_location_errors_rk()
clear; clc;

fprintf('=== 基于Runge-Kutta射线追踪法的地震定位误差分析 ===\n\n');

% 设置随机种子以确保结果可重复
rng(12345, 'twister');  % 固定随机种子
fprintf('已设置随机种子，确保结果可重复\n\n');

% ===== 1. 设置多个测试震源 =====
test_sources = [
    100, 150;   % 测试点1
    200, 300;   % 测试点2
    350, 250;   % 测试点3
    150, 400;   % 测试点4
    300, 100    % 测试点5
    ];

num_tests = size(test_sources, 1);

% 存储结果
location_errors = zeros(num_tests, 1);
estimated_sources = zeros(num_tests, 2);

% ===== 2. 建立速度模型和台站网络 =====
fprintf('建立速度模型和台站网络...\n');

% 横坐标范围
xx = 1:500;

% ----------------------
% 第二条线 10点插值
x2 = [0, 43, 85, 170, 260, 289, 347, 404, 468, 500];
y2 = [44, 50, 56, 55, 56, 55, 57, 53, 62, 65];
yy2 = round(spline(x2, y2, xx));

% 第三条线 10点插值
x3 = [0, 34, 55, 104, 137, 212, 296, 403, 482, 500];
y3 = [98, 94, 101, 100, 101, 109, 111, 118, 108, 109];
yy3 = round(spline(x3, y3, xx));

% 第四条线（13点）插值
x4 = [0, 51, 85, 102, 192, 211, 238, 276, 310, 337, 351, 418, 476, 500];
y4 = [150, 160, 160, 155, 153, 160, 151, 151, 149, 153, 148, 152, 145, 148];
yy4 = round(spline(x4, y4, xx));

% 第五条线（11点）插值
x5 = [0, 25,46, 128, 200, 294, 339, 378, 425, 465, 500];
y5 = [160,169, 174, 173, 171, 172, 173, 171, 172, 169, 167];
yy5 = round(spline(x5, y5, xx));

% ----------------------
% 限制在图像边界范围内
yy2 = min(max(yy2, 1), 500);
yy3 = min(max(yy3, 1), 500);
yy4 = min(max(yy4, 1), 500);
yy5 = min(max(yy5, 1), 500);

% ----------------------
% 创建速度图像
SpeedImage = zeros(500, 500);

for col = 1:500
    SpeedImage(1:yy2(col), col) = 1500;
    SpeedImage(yy2(col)+1:yy3(col), col) = 3000;
    SpeedImage(yy3(col)+1:yy4(col), col) = 4200;
    SpeedImage(yy4(col)+1:yy5(col), col) = 5400;
    SpeedImage(yy5(col)+1:end, col) = 6000;
end

% 大空洞
SpeedImage(341:366, 340:350) = 340;
SpeedImage(341:366, 315:325) = 340;
SpeedImage(341:366, 285:295) = 340;
SpeedImage(341:366, 245:255) = 340;
SpeedImage(341:366, 220:230) = 340;
SpeedImage(341:366, 180:190) = 340;
SpeedImage(341:366, 150:160) = 340;
SpeedImage(341:366, 120:130) = 340;

% 小空洞
SpeedImage(316:321, 355:360) = 340;
SpeedImage(316:321, 295:300) = 340;
SpeedImage(316:321, 230:235) = 340;
SpeedImage(316:321, 160:165) = 340;
SpeedImage(316:321, 105:110) = 340;

% 中间空洞
SpeedImage(316:321, 110:200) = 340;
SpeedImage(316:321, 195:200) = 340;
SpeedImage(316:321, 195:270) = 340;
SpeedImage(316:321, 265:270) = 340;
SpeedImage(316:321, 265:360) = 340;

% 完整倒转速度模型
SpeedImage = rot90(SpeedImage, 2); 

stations = [50, 50; 450, 50; 450, 450; 50, 450; 250, 100; 250, 400];
num_stations = size(stations, 1);

fprintf('速度模型大小: %d × %d\n', size(SpeedImage, 1), size(SpeedImage, 2));
fprintf('台站数量: %d\n', num_stations);

% ===== 3. 使用RK射线追踪计算各台站的走时表 =====
fprintf('\n使用Runge-Kutta射线追踪计算各台站的走时表...\n');
travel_time_tables = cell(num_stations, 1);

for i = 1:num_stations
    tic;
    fprintf('正在计算台站 %d 的走时表...', i);
    
    try
        % 使用RK射线追踪替代msfm2d
        travel_time_tables{i} = rk_raytracing2d(SpeedImage, stations(i,:)', true, true);
        elapsed_time = toc;
        fprintf(' 完成，用时: %.2f秒\n', elapsed_time);

        % 验证走时表的合理性
        validate_travel_time_table(travel_time_tables{i}, stations(i,:), i);

    catch ME
        fprintf(' 失败: %s\n', ME.message);
        fprintf('尝试使用简化的射线追踪方法...\n');
        travel_time_tables{i} = compute_travel_times_simple(SpeedImage, stations(i,:)');
        elapsed_time = toc;
        fprintf('简化方法完成，用时: %.2f秒\n', elapsed_time);

        % 验证简化方法的走时表
        validate_travel_time_table(travel_time_tables{i}, stations(i,:), i);
    end
end

% ===== 4. 对每个测试震源进行定位 =====
fprintf('\n开始对各测试震源进行定位...\n');

for test_idx = 1:num_tests
    true_source = test_sources(test_idx, :);
    fprintf('\n=== 测试震源 %d: [%d, %d] ===\n', test_idx, true_source(1), true_source(2));

    % 生成观测数据
    observed_times = generate_observed_times_rk(true_source, travel_time_tables, stations);

    % 执行定位
    estimated_source = grid_search_location_rk(observed_times, travel_time_tables, stations);

    % 计算误差
    error = norm(true_source - estimated_source);

    % 存储结果
    location_errors(test_idx) = error;
    estimated_sources(test_idx, :) = estimated_source;

    % 输出单个结果
    fprintf('真实位置: [%d, %d]\n', true_source(1), true_source(2));
    fprintf('定位结果: [%d, %d]\n', estimated_source(1), estimated_source(2));
    fprintf('定位误差: %.2f 网格单位\n', error);

    % 检查误差是否合理
    if error == 0
        fprintf('⚠️  警告: 定位误差为0，可能存在过拟合问题\n');
    elseif error > 20
        fprintf('⚠️  警告: 定位误差过大 (>20)，可能存在算法问题\n');
    elseif error <= 15
        fprintf('✅ 定位误差在合理范围内 (≤15)\n');
    else
        fprintf('⚠️  定位误差偏大但可接受 (15-20)\n');
    end
end

% ===== 5. 统计分析 =====
fprintf('\n=== 误差统计分析 ===\n');
mean_error = mean(location_errors);
std_error = std(location_errors);
max_error = max(location_errors);
min_error = min(location_errors);
rms_error = sqrt(mean(location_errors.^2));

fprintf('平均误差: %.2f 网格单位\n', mean_error);
fprintf('误差标准差: %.2f 网格单位\n', std_error);
fprintf('最大误差: %.2f 网格单位\n', max_error);
fprintf('最小误差: %.2f 网格单位\n', min_error);
fprintf('RMS误差: %.2f 网格单位\n', rms_error);

% 误差合理性评估
fprintf('\n=== 误差合理性评估 ===\n');
zero_errors = sum(location_errors == 0);
reasonable_errors = sum(location_errors <= 15);
large_errors = sum(location_errors > 20);

fprintf('零误差点数: %d/%d (%.1f%%)\n', zero_errors, num_tests, 100*zero_errors/num_tests);
fprintf('合理误差点数 (≤15): %d/%d (%.1f%%)\n', reasonable_errors, num_tests, 100*reasonable_errors/num_tests);
fprintf('过大误差点数 (>20): %d/%d (%.1f%%)\n', large_errors, num_tests, 100*large_errors/num_tests);

if zero_errors > num_tests * 0.3
    fprintf('⚠️  警告: 零误差点过多，可能存在过拟合\n');
end

if large_errors > num_tests * 0.2
    fprintf('⚠️  警告: 大误差点过多，算法可能需要调优\n');
end

if reasonable_errors == num_tests
    fprintf('✅ 所有测试点误差都在合理范围内\n');
elseif reasonable_errors >= num_tests * 0.8
    fprintf('✅ 大部分测试点误差在合理范围内\n');
else
    fprintf('⚠️  需要进一步优化算法参数\n');
end

% ===== 6. 可视化结果 =====
fprintf('\n生成可视化结果...\n');
visualize_results_rk(SpeedImage, stations, test_sources, estimated_sources, location_errors);

fprintf('\n=== Runge-Kutta射线追踪法地震定位误差分析完成 ===\n');
end

function observed_times = generate_observed_times_rk(true_source, travel_time_tables, stations, varargin)
% 生成观测走时数据（RK版本）
% 输入参数:
%   true_source - 真实震源位置
%   travel_time_tables - 走时表
%   stations - 台站位置
%   varargin - 可选参数：
%     'NoiseLevel' - 噪声水平 (默认: 0.002)
%     'UseFixedNoise' - 是否使用固定噪声 (默认: true)

% 解析输入参数
p = inputParser;
addParameter(p, 'NoiseLevel', 0.002, @isnumeric);
addParameter(p, 'UseFixedNoise', true, @islogical);
parse(p, varargin{:});

noise_level_base = p.Results.NoiseLevel;
use_fixed_noise = p.Results.UseFixedNoise;

num_stations = length(travel_time_tables);
observed_times = zeros(num_stations, 1);

fprintf('生成观测数据 - 真实震源: [%d, %d]\n', true_source(1), true_source(2));
if use_fixed_noise
    fprintf('使用固定噪声模式，结果可重复\n');
else
    fprintf('使用随机噪声模式，每次结果不同\n');
end

for i = 1:num_stations
    % 提取理论走时
    if true_source(1) >= 1 && true_source(1) <= size(travel_time_tables{i}, 1) && ...
       true_source(2) >= 1 && true_source(2) <= size(travel_time_tables{i}, 2)
        observed_times(i) = travel_time_tables{i}(true_source(1), true_source(2));

        % 检查走时是否合理
        if ~isfinite(observed_times(i)) || observed_times(i) <= 0
            fprintf('  警告: 台站%d走时无效 (%.6f)，使用距离估算\n', i, observed_times(i));
            dist = norm(true_source - stations(i, :));
            observed_times(i) = dist / 3000; % 假设平均速度3000 m/s
        end
    else
        % 如果超出边界，使用简单估算
        fprintf('  警告: 震源超出台站%d走时表边界，使用距离估算\n', i);
        dist = norm(true_source - stations(i, :));
        observed_times(i) = dist / 3000; % 假设平均速度3000 m/s
    end

    % 添加观测噪声
    if use_fixed_noise
        % 使用基于台站编号和震源位置的确定性噪声
        noise_seed = true_source(1) * 1000 + true_source(2) * 100 + i * 10;
        rng(noise_seed, 'twister');
        noise = noise_level_base * randn();
        rng('shuffle'); % 恢复随机状态
    else
        % 使用真正的随机噪声
        noise_level = noise_level_base + 0.001 * observed_times(i); % 相对噪声
        noise = noise_level * randn();
    end

    observed_times(i) = observed_times(i) + noise;

    % 确保走时为正值
    observed_times(i) = max(observed_times(i), 1e-6);

    fprintf('  台站%d: 理论走时=%.4f, 噪声=%.4f, 观测走时=%.4f\n', ...
            i, observed_times(i)-noise, noise, observed_times(i));
end

fprintf('观测数据生成完成\n\n');
end

function best_location = grid_search_location_rk(observed_times, travel_time_tables, stations)
% 网格搜索定位（RK版本）
search_range_x = 60:8:440;  % 避免边界，使用8个网格单位步长
search_range_y = 60:8:440;

min_residual = inf;
best_location = [0, 0];
residual_count = 0;
valid_count = 0;

fprintf('执行网格搜索定位...\n');
fprintf('观测走时: [%.4f, %.4f, %.4f, %.4f, %.4f, %.4f]\n', observed_times);

total_points = length(search_range_x) * length(search_range_y);
completed = 0;

% 存储前几个最佳位置用于调试
best_locations = [];
best_residuals = [];

for i = 1:length(search_range_x)
    for j = 1:length(search_range_y)
        x = search_range_x(i);
        y = search_range_y(j);

        % 计算理论走时
        theoretical_times = zeros(length(travel_time_tables), 1);
        valid_times = true;

        for k = 1:length(travel_time_tables)
            if x >= 1 && x <= size(travel_time_tables{k}, 1) && ...
               y >= 1 && y <= size(travel_time_tables{k}, 2)
                theoretical_times(k) = travel_time_tables{k}(x, y);

                % 检查走时是否有效
                if ~isfinite(theoretical_times(k)) || theoretical_times(k) <= 0
                    valid_times = false;
                    break;
                end
            else
                valid_times = false;
                break;
            end
        end

        if valid_times
            valid_count = valid_count + 1;

            % 计算残差
            residual = sqrt(mean((observed_times - theoretical_times).^2));
            residual_count = residual_count + 1;

            % 保存前10个最佳结果用于调试
            if length(best_residuals) < 10
                best_locations = [best_locations; x, y];
                best_residuals = [best_residuals; residual];
            else
                [max_res, max_idx] = max(best_residuals);
                if residual < max_res
                    best_locations(max_idx, :) = [x, y];
                    best_residuals(max_idx) = residual;
                end
            end

            if residual < min_residual
                min_residual = residual;
                best_location = [x, y];

                % 输出当前最佳结果的详细信息
                if mod(residual_count, 100) == 0 || residual < 0.01
                    fprintf('  新的最佳位置: [%d, %d], 残差: %.6f\n', x, y, residual);
                    fprintf('    理论走时: [%.4f, %.4f, %.4f, %.4f, %.4f, %.4f]\n', theoretical_times);
                end
            end
        end

        completed = completed + 1;
        if mod(completed, 2000) == 0
            fprintf('搜索进度: %.1f%%, 有效点: %d, 当前最佳残差: %.6f\n', ...
                   100 * completed / total_points, valid_count, min_residual);
        end
    end
end

fprintf('\n网格搜索完成:\n');
fprintf('  总搜索点: %d\n', total_points);
fprintf('  有效点: %d\n', valid_count);
fprintf('  最佳位置: [%d, %d]\n', best_location(1), best_location(2));
fprintf('  最小残差: %.6f秒\n', min_residual);

% 显示前几个最佳结果
fprintf('\n前5个最佳结果:\n');
[sorted_residuals, sort_idx] = sort(best_residuals);
for i = 1:min(5, length(sorted_residuals))
    idx = sort_idx(i);
    fprintf('  位置[%d, %d]: 残差=%.6f\n', ...
           best_locations(idx, 1), best_locations(idx, 2), sorted_residuals(i));
end
fprintf('\n');
end

function T = compute_travel_times_simple(SpeedImage, source_pos)
% 改进的简化走时计算函数
fprintf('使用改进的简化射线追踪方法...\n');

[rows, cols] = size(SpeedImage);
T = inf(rows, cols);

source_x = round(source_pos(1));
source_y = round(source_pos(2));

% 确保震源在有效范围内
source_x = max(1, min(rows, source_x));
source_y = max(1, min(cols, source_y));

% 设置震源点走时为0
T(source_x, source_y) = 0;

% 使用改进的射线追踪方法
for i = 1:rows
    for j = 1:cols
        if i == source_x && j == source_y
            continue;
        end

        % 计算多条路径的走时，取最小值
        min_time = inf;

        % 路径1: 直线路径
        time1 = compute_path_time(SpeedImage, source_x, source_y, i, j, 'direct');
        min_time = min(min_time, time1);

        % 路径2: 先水平后垂直
        time2 = compute_path_time(SpeedImage, source_x, source_y, i, j, 'horizontal_first');
        min_time = min(min_time, time2);

        % 路径3: 先垂直后水平
        time3 = compute_path_time(SpeedImage, source_x, source_y, i, j, 'vertical_first');
        min_time = min(min_time, time3);

        T(i, j) = min_time;
    end
end

fprintf('改进的简化走时计算完成\n');
end

function travel_time = compute_path_time(SpeedImage, x1, y1, x2, y2, path_type)
% 计算特定路径的走时
[rows, cols] = size(SpeedImage);

switch path_type
    case 'direct'
        % 直线路径
        dx = x2 - x1;
        dy = y2 - y1;
        num_points = max(abs(dx), abs(dy)) * 2 + 1; % 更密集的采样

        if num_points <= 1
            travel_time = 0;
            return;
        end

        x_step = dx / (num_points - 1);
        y_step = dy / (num_points - 1);

        total_time = 0;
        for step = 1:num_points-1
            x_pos = x1 + step * x_step;
            y_pos = y1 + step * y_step;

            % 双线性插值获取速度
            velocity = interpolate_velocity_safe(SpeedImage, x_pos, y_pos);

            % 计算这一段的走时
            segment_distance = sqrt(x_step^2 + y_step^2);
            total_time = total_time + segment_distance / velocity;
        end
        travel_time = total_time;

    case 'horizontal_first'
        % 先水平移动到目标列，再垂直移动
        time1 = compute_path_time(SpeedImage, x1, y1, x1, y2, 'direct');
        time2 = compute_path_time(SpeedImage, x1, y2, x2, y2, 'direct');
        travel_time = time1 + time2;

    case 'vertical_first'
        % 先垂直移动到目标行，再水平移动
        time1 = compute_path_time(SpeedImage, x1, y1, x2, y1, 'direct');
        time2 = compute_path_time(SpeedImage, x2, y1, x2, y2, 'direct');
        travel_time = time1 + time2;

    otherwise
        travel_time = inf;
end
end

function velocity = interpolate_velocity_safe(SpeedImage, x, y)
% 安全的速度插值函数
[rows, cols] = size(SpeedImage);

% 边界处理
x = max(1, min(rows, x));
y = max(1, min(cols, y));

% 双线性插值
x1 = floor(x); x2 = min(rows, x1 + 1);
y1 = floor(y); y2 = min(cols, y1 + 1);

fx = x - x1;
fy = y - y1;

v11 = SpeedImage(x1, y1);
v12 = SpeedImage(x1, y2);
v21 = SpeedImage(x2, y1);
v22 = SpeedImage(x2, y2);

velocity = v11 * (1-fx) * (1-fy) + v21 * fx * (1-fy) + ...
           v12 * (1-fx) * fy + v22 * fx * fy;

% 确保速度为正值
velocity = max(velocity, 500); % 最小速度500 m/s
end

function validate_travel_time_table(T, station_pos, station_id)
% 验证走时表的合理性
fprintf('验证台站%d的走时表...\n', station_id);

% 基本统计
valid_times = T(isfinite(T) & T > 0);
if isempty(valid_times)
    fprintf('  错误: 没有有效的走时值!\n');
    return;
end

fprintf('  有效走时数量: %d / %d\n', length(valid_times), numel(T));
fprintf('  走时范围: %.4f - %.4f 秒\n', min(valid_times), max(valid_times));
fprintf('  平均走时: %.4f 秒\n', mean(valid_times));

% 检查台站位置的走时
station_x = round(station_pos(1));
station_y = round(station_pos(2));

if station_x >= 1 && station_x <= size(T, 1) && ...
   station_y >= 1 && station_y <= size(T, 2)
    station_time = T(station_x, station_y);
    fprintf('  台站位置走时: %.6f 秒 (应该接近0)\n', station_time);

    if abs(station_time) > 0.001
        fprintf('  警告: 台站位置走时异常!\n');
    end
else
    fprintf('  警告: 台站位置超出走时表范围!\n');
end

% 检查走时的连续性（随机采样几个点）
sample_points = 10;
discontinuities = 0;
for i = 1:sample_points
    x = randi([2, size(T,1)-1]);
    y = randi([2, size(T,2)-1]);

    center_time = T(x, y);
    neighbor_times = [T(x-1,y), T(x+1,y), T(x,y-1), T(x,y+1)];

    if isfinite(center_time) && all(isfinite(neighbor_times))
        max_diff = max(abs(neighbor_times - center_time));
        if max_diff > 0.1  % 如果相邻点走时差异超过0.1秒
            discontinuities = discontinuities + 1;
        end
    end
end

if discontinuities > sample_points * 0.3
    fprintf('  警告: 走时表可能存在不连续性 (%d/%d 采样点异常)\n', ...
           discontinuities, sample_points);
end

fprintf('  台站%d走时表验证完成\n\n', station_id);
end

function visualize_results_rk(SpeedImage, stations, true_sources, estimated_sources, errors)
% 可视化结果（RK版本）
figure('Name', 'Runge-Kutta射线追踪法地震定位误差分析', 'Position', [100, 100, 1200, 800]);

% 主图：显示所有结果
subplot(2, 2, [1, 2]);
pcolor(SpeedImage); shading interp; colormap(jet); colorbar;
hold on;

% 绘制台站
plot(stations(:,2), stations(:,1), '^k', 'MarkerSize', 12, 'MarkerFaceColor', 'k');

% 绘制真实震源和定位结果
colors = ['r', 'g', 'b', 'm', 'c'];
for i = 1:size(true_sources, 1)
    % 真实震源
    plot(true_sources(i,2), true_sources(i,1), '*', 'Color', colors(i), ...
        'MarkerSize', 15, 'LineWidth', 2);
    % 定位结果
    plot(estimated_sources(i,2), estimated_sources(i,1), 'o', 'Color', colors(i), ...
        'MarkerSize', 10, 'LineWidth', 2);
    % 连接线显示误差
    plot([true_sources(i,2), estimated_sources(i,2)], ...
        [true_sources(i,1), estimated_sources(i,1)], '--', 'Color', colors(i), 'LineWidth', 1);

    % 标注误差值
    mid_x = (true_sources(i,2) + estimated_sources(i,2)) / 2;
    mid_y = (true_sources(i,1) + estimated_sources(i,1)) / 2;
    text(mid_x, mid_y, sprintf('%.1f', errors(i)), 'FontSize', 10, 'FontWeight', 'bold', ...
         'BackgroundColor', 'white', 'EdgeColor', 'black');
end

title('RK射线追踪法地震定位结果对比');
xlabel('Y坐标'); ylabel('X坐标');
legend('速度模型', '台站', '真实震源', '定位结果', 'Location', 'best');

% 误差分布图
subplot(2, 2, 3);
bar(1:length(errors), errors, 'FaceColor', [0.2, 0.6, 0.8]);
title('各测试点的定位误差 (RK射线追踪)');
xlabel('测试点编号'); ylabel('误差 (网格单位)');
grid on;
for i = 1:length(errors)
    text(i, errors(i) + 0.5, sprintf('%.1f', errors(i)), ...
         'HorizontalAlignment', 'center', 'FontWeight', 'bold');
end

% 误差统计直方图
subplot(2, 2, 4);
histogram(errors, 'BinWidth', 1, 'FaceColor', [0.8, 0.4, 0.2]);
title('误差分布直方图 (RK射线追踪)');
xlabel('误差 (网格单位)'); ylabel('频次');
grid on;

% 添加统计信息
mean_error = mean(errors);
std_error = std(errors);
max_error = max(errors);
min_error = min(errors);

text(0.05, 0.85, sprintf('平均误差: %.2f', mean_error), 'Units', 'normalized', ...
     'FontSize', 10, 'FontWeight', 'bold', 'BackgroundColor', 'white');
text(0.05, 0.75, sprintf('标准差: %.2f', std_error), 'Units', 'normalized', ...
     'FontSize', 10, 'FontWeight', 'bold', 'BackgroundColor', 'white');
text(0.05, 0.65, sprintf('最大误差: %.2f', max_error), 'Units', 'normalized', ...
     'FontSize', 10, 'FontWeight', 'bold', 'BackgroundColor', 'white');
text(0.05, 0.55, sprintf('最小误差: %.2f', min_error), 'Units', 'normalized', ...
     'FontSize', 10, 'FontWeight', 'bold', 'BackgroundColor', 'white');

% 添加总标题
sgtitle('基于Runge-Kutta射线追踪法的地震定位误差分析', 'FontSize', 14, 'FontWeight', 'bold');

% 保存图形
try
    saveas(gcf, 'rk_raytracing_location_analysis.png');
    fprintf('结果图已保存为: rk_raytracing_location_analysis.png\n');
catch
    fprintf('图形保存失败，但显示正常\n');
end

% 创建详细的误差分析图
figure('Name', 'RK射线追踪详细误差分析', 'Position', [150, 150, 1000, 600]);

% 误差随测试点的变化
subplot(2, 3, 1);
plot(1:length(errors), errors, 'o-', 'LineWidth', 2, 'MarkerSize', 8, 'MarkerFaceColor', 'red');
title('定位误差趋势');
xlabel('测试点编号'); ylabel('误差 (网格单位)');
grid on;

% 真实位置 vs 估计位置 (X坐标)
subplot(2, 3, 2);
scatter(true_sources(:,1), estimated_sources(:,1), 100, errors, 'filled');
colorbar; colormap(hot);
hold on;
plot([min(true_sources(:,1)), max(true_sources(:,1))], ...
     [min(true_sources(:,1)), max(true_sources(:,1))], 'k--', 'LineWidth', 2);
title('X坐标对比');
xlabel('真实X坐标'); ylabel('估计X坐标');

% 真实位置 vs 估计位置 (Y坐标)
subplot(2, 3, 3);
scatter(true_sources(:,2), estimated_sources(:,2), 100, errors, 'filled');
colorbar; colormap(hot);
hold on;
plot([min(true_sources(:,2)), max(true_sources(:,2))], ...
     [min(true_sources(:,2)), max(true_sources(:,2))], 'k--', 'LineWidth', 2);
title('Y坐标对比');
xlabel('真实Y坐标'); ylabel('估计Y坐标');

% 误差向量图
subplot(2, 3, [4, 5, 6]);
quiver(true_sources(:,2), true_sources(:,1), ...
       estimated_sources(:,2) - true_sources(:,2), ...
       estimated_sources(:,1) - true_sources(:,1), ...
       0, 'LineWidth', 2, 'MaxHeadSize', 0.5);
hold on;
plot(true_sources(:,2), true_sources(:,1), 'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'red');
plot(estimated_sources(:,2), estimated_sources(:,1), 'bs', 'MarkerSize', 8, 'MarkerFaceColor', 'blue');

% 添加误差标注
for i = 1:size(true_sources, 1)
    text(true_sources(i,2) + 5, true_sources(i,1) + 5, ...
         sprintf('%.1f', errors(i)), 'FontSize', 10, 'FontWeight', 'bold');
end

title('定位误差向量图');
xlabel('Y坐标'); ylabel('X坐标');
legend('误差向量', '真实位置', '估计位置', 'Location', 'best');
grid on;
axis equal;

fprintf('详细误差分析图已生成\n');
end
