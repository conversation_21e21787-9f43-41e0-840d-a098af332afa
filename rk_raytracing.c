#include "mex.h"
#include "math.h"
#include <stdlib.h>
#include <string.h>

#define PI 3.14159265358979323846
#define MAX_STEPS 10000
#define STEP_SIZE 0.5
#define TOLERANCE 1e-6
#define INF 1e10

/* 射线状态结构体 */
typedef struct {
    double x, y;        /* 位置 */
    double px, py;      /* 慢度分量 */
    double t;           /* 走时 */
} RayState;

/* 速度模型插值函数 */
double interpolate_velocity(double *SpeedImage, int *dims, double x, double y) {
    int i1, j1, i2, j2;
    double fx, fy, v11, v12, v21, v22;
    
    /* 边界检查 */
    if (x < 0 || x >= dims[0]-1 || y < 0 || y >= dims[1]-1) {
        return 1500.0; /* 默认速度 */
    }
    
    /* 双线性插值 */
    i1 = (int)floor(x);
    j1 = (int)floor(y);
    i2 = i1 + 1;
    j2 = j1 + 1;
    
    fx = x - i1;
    fy = y - j1;
    
    v11 = SpeedImage[i1 + j1 * dims[0]];
    v12 = SpeedImage[i1 + j2 * dims[0]];
    v21 = SpeedImage[i2 + j1 * dims[0]];
    v22 = SpeedImage[i2 + j2 * dims[0]];
    
    return v11 * (1-fx) * (1-fy) + v21 * fx * (1-fy) + 
           v12 * (1-fx) * fy + v22 * fx * fy;
}

/* 计算速度梯度 */
void compute_velocity_gradient(double *SpeedImage, int *dims, double x, double y, 
                              double *grad_x, double *grad_y) {
    double h = 1.0; /* 差分步长 */
    double v_xp, v_xm, v_yp, v_ym;
    
    v_xp = interpolate_velocity(SpeedImage, dims, x + h, y);
    v_xm = interpolate_velocity(SpeedImage, dims, x - h, y);
    v_yp = interpolate_velocity(SpeedImage, dims, x, y + h);
    v_ym = interpolate_velocity(SpeedImage, dims, x, y - h);
    
    *grad_x = (v_xp - v_xm) / (2.0 * h);
    *grad_y = (v_yp - v_ym) / (2.0 * h);
}

/* 射线方程右端项 */
void ray_equations(double *SpeedImage, int *dims, RayState *state, RayState *deriv) {
    double v, grad_x, grad_y;
    double v3;
    
    v = interpolate_velocity(SpeedImage, dims, state->x, state->y);
    compute_velocity_gradient(SpeedImage, dims, state->x, state->y, &grad_x, &grad_y);
    
    v3 = v * v * v;
    
    /* dx/dt = v^2 * px */
    deriv->x = v * v * state->px;
    
    /* dy/dt = v^2 * py */
    deriv->y = v * v * state->py;
    
    /* dpx/dt = -grad_x / v^3 */
    deriv->px = -grad_x / v3;
    
    /* dpy/dt = -grad_y / v^3 */
    deriv->py = -grad_y / v3;
    
    /* dt/dt = 1 (参数时间) */
    deriv->t = 1.0 / v;
}

/* 四阶Runge-Kutta积分器 */
void runge_kutta_step(double *SpeedImage, int *dims, RayState *state, double dt) {
    RayState k1, k2, k3, k4, temp_state;
    
    /* k1 = f(t, y) */
    ray_equations(SpeedImage, dims, state, &k1);
    
    /* k2 = f(t + dt/2, y + dt*k1/2) */
    temp_state.x = state->x + dt * k1.x / 2.0;
    temp_state.y = state->y + dt * k1.y / 2.0;
    temp_state.px = state->px + dt * k1.px / 2.0;
    temp_state.py = state->py + dt * k1.py / 2.0;
    temp_state.t = state->t + dt * k1.t / 2.0;
    ray_equations(SpeedImage, dims, &temp_state, &k2);
    
    /* k3 = f(t + dt/2, y + dt*k2/2) */
    temp_state.x = state->x + dt * k2.x / 2.0;
    temp_state.y = state->y + dt * k2.y / 2.0;
    temp_state.px = state->px + dt * k2.px / 2.0;
    temp_state.py = state->py + dt * k2.py / 2.0;
    temp_state.t = state->t + dt * k2.t / 2.0;
    ray_equations(SpeedImage, dims, &temp_state, &k3);
    
    /* k4 = f(t + dt, y + dt*k3) */
    temp_state.x = state->x + dt * k3.x;
    temp_state.y = state->y + dt * k3.y;
    temp_state.px = state->px + dt * k3.px;
    temp_state.py = state->py + dt * k3.py;
    temp_state.t = state->t + dt * k3.t;
    ray_equations(SpeedImage, dims, &temp_state, &k4);
    
    /* 更新状态 */
    state->x += dt * (k1.x + 2*k2.x + 2*k3.x + k4.x) / 6.0;
    state->y += dt * (k1.y + 2*k2.y + 2*k3.y + k4.y) / 6.0;
    state->px += dt * (k1.px + 2*k2.px + 2*k3.px + k4.px) / 6.0;
    state->py += dt * (k1.py + 2*k2.py + 2*k3.py + k4.py) / 6.0;
    state->t += dt * (k1.t + 2*k2.t + 2*k3.t + k4.t) / 6.0;
}

/* 射线追踪函数 */
double trace_ray(double *SpeedImage, int *dims, double start_x, double start_y, 
                 double end_x, double end_y) {
    RayState ray;
    double dx, dy, dist, angle;
    double v0;
    int step;
    double dt = STEP_SIZE;
    double target_dist_sq, current_dist_sq;
    double min_dist = INF;
    double best_time = INF;
    
    /* 计算初始方向 */
    dx = end_x - start_x;
    dy = end_y - start_y;
    dist = sqrt(dx*dx + dy*dy);
    
    if (dist < TOLERANCE) {
        return 0.0; /* 起点和终点相同 */
    }
    
    /* 初始化射线状态 */
    ray.x = start_x;
    ray.y = start_y;
    ray.t = 0.0;
    
    /* 获取起始点速度 */
    v0 = interpolate_velocity(SpeedImage, dims, start_x, start_y);
    
    /* 初始慢度方向（单位向量/速度） */
    angle = atan2(dy, dx);
    ray.px = cos(angle) / v0;
    ray.py = sin(angle) / v0;
    
    target_dist_sq = dx*dx + dy*dy;
    
    /* 射线追踪主循环 */
    for (step = 0; step < MAX_STEPS; step++) {
        /* 检查是否到达目标 */
        dx = ray.x - end_x;
        dy = ray.y - end_y;
        current_dist_sq = dx*dx + dy*dy;
        
        if (current_dist_sq < min_dist) {
            min_dist = current_dist_sq;
            best_time = ray.t;
        }
        
        if (sqrt(current_dist_sq) < TOLERANCE) {
            return ray.t;
        }
        
        /* 检查边界 */
        if (ray.x < 0 || ray.x >= dims[0] || ray.y < 0 || ray.y >= dims[1]) {
            break;
        }
        
        /* 如果距离开始增大，说明已经过了最近点 */
        if (step > 100 && current_dist_sq > 4 * min_dist) {
            break;
        }
        
        /* Runge-Kutta积分步 */
        runge_kutta_step(SpeedImage, dims, &ray, dt);
    }
    
    return best_time;
}

/* 多角度射线追踪（提高精度） */
double multi_angle_raytracing(double *SpeedImage, int *dims, double start_x, double start_y,
                             double end_x, double end_y) {
    int num_angles = 8;
    double best_time = INF;
    double angle_step = 2 * PI / num_angles;
    double base_angle = atan2(end_y - start_y, end_x - start_x);
    double angle_range = PI / 6; /* ±30度范围 */
    int i;

    for (i = 0; i < num_angles; i++) {
        double angle_offset = (i - num_angles/2) * angle_range / (num_angles/2);
        double current_angle = base_angle + angle_offset;

        RayState ray;
        double v0, dx, dy, current_dist_sq;
        double min_dist = INF;
        double current_time = INF;
        int step;

        /* 初始化射线 */
        ray.x = start_x;
        ray.y = start_y;
        ray.t = 0.0;

        v0 = interpolate_velocity(SpeedImage, dims, start_x, start_y);
        ray.px = cos(current_angle) / v0;
        ray.py = sin(current_angle) / v0;

        /* 射线追踪 */
        for (step = 0; step < MAX_STEPS; step++) {
            dx = ray.x - end_x;
            dy = ray.y - end_y;
            current_dist_sq = dx*dx + dy*dy;

            if (current_dist_sq < min_dist) {
                min_dist = current_dist_sq;
                current_time = ray.t;
            }

            if (sqrt(current_dist_sq) < TOLERANCE) {
                current_time = ray.t;
                break;
            }

            if (ray.x < 0 || ray.x >= dims[0] || ray.y < 0 || ray.y >= dims[1]) {
                break;
            }

            if (step > 100 && current_dist_sq > 4 * min_dist) {
                break;
            }

            runge_kutta_step(SpeedImage, dims, &ray, STEP_SIZE);
        }

        if (current_time < best_time) {
            best_time = current_time;
        }
    }

    return best_time;
}

/* 计算走时表的主函数 */
void compute_travel_time_table(double *SpeedImage, int *dims, double source_x, double source_y,
                              double *TravelTimeTable) {
    int i, j;
    int total_points = dims[0] * dims[1];
    int completed = 0;

    for (i = 0; i < dims[0]; i++) {
        for (j = 0; j < dims[1]; j++) {
            int index = i + j * dims[0];

            if (i == (int)source_x && j == (int)source_y) {
                TravelTimeTable[index] = 0.0;
            } else {
                /* 使用多角度射线追踪提高精度 */
                TravelTimeTable[index] = multi_angle_raytracing(SpeedImage, dims,
                                                               source_x, source_y,
                                                               (double)i, (double)j);
            }

            completed++;
            if (completed % 1000 == 0) {
                mexPrintf("Progress: %.1f%%\n", 100.0 * completed / total_points);
                mexEvalString("drawnow;");
            }
        }
    }
}

/* MATLAB MEX接口函数 */
void mexFunction(int nlhs, mxArray *plhs[], int nrhs, const mxArray *prhs[]) {
    double *SpeedImage, *SourcePoints, *TravelTimeTable;
    int *dims;
    const mwSize *dims_c, *dims_sp_c;
    mwSize dims_out[2];
    int num_sources, i;

    /* 检查输入参数 */
    if (nrhs < 2) {
        mexErrMsgTxt("至少需要2个输入参数：速度模型和震源位置");
    }

    if (nlhs != 1) {
        mexErrMsgTxt("需要1个输出参数：走时表");
    }

    /* 检查输入数据类型 */
    if (!mxIsDouble(prhs[0]) || !mxIsDouble(prhs[1])) {
        mexErrMsgTxt("输入参数必须是double类型");
    }

    /* 获取速度模型 */
    SpeedImage = mxGetPr(prhs[0]);
    dims_c = mxGetDimensions(prhs[0]);

    if (mxGetNumberOfDimensions(prhs[0]) != 2) {
        mexErrMsgTxt("速度模型必须是2D矩阵");
    }

    dims = (int*)malloc(2 * sizeof(int));
    dims[0] = (int)dims_c[0];
    dims[1] = (int)dims_c[1];

    /* 获取震源位置 */
    SourcePoints = mxGetPr(prhs[1]);
    dims_sp_c = mxGetDimensions(prhs[1]);

    if (dims_sp_c[0] != 2) {
        mexErrMsgTxt("震源位置必须是2xN矩阵");
    }

    num_sources = (int)dims_sp_c[1];

    /* 创建输出走时表 */
    dims_out[0] = dims_c[0];
    dims_out[1] = dims_c[1];
    plhs[0] = mxCreateNumericArray(2, dims_out, mxDOUBLE_CLASS, mxREAL);
    TravelTimeTable = mxGetPr(plhs[0]);

    /* 目前只处理单个震源 */
    if (num_sources > 1) {
        mexWarnMsgTxt("当前版本只处理第一个震源");
    }

    /* 计算走时表 */
    mexPrintf("开始Runge-Kutta射线追踪计算...\n");
    compute_travel_time_table(SpeedImage, dims,
                             SourcePoints[0] - 1, SourcePoints[1] - 1,
                             TravelTimeTable);
    mexPrintf("计算完成!\n");

    /* 释放内存 */
    free(dims);
}
