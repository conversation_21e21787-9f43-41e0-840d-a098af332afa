%% 六种方法完整比较：MSFM3D vs 几何分析法 vs Dijkstra vs A* vs Dial vs DAG
% 全面比较六种3D传播时间计算方法

clear; clc;

fprintf('=== 六种方法完整比较测试 ===\n');
fprintf('1. MSFM3D (快速行进方法)\n');
fprintf('2. 几何分析法 (直接计算)\n');
fprintf('3. Dijkstra算法 (图论最短路径)\n');
fprintf('4. A*算法 (启发式搜索)\n');
fprintf('5. Dial算法 (优化的Dijkstra)\n');
fprintf('6. DAG算法 (有向无环图最短路径)\n\n');

%% 测试参数设置
SourcePoint = [1; 1; 1];
SpeedImage = ones([200 200 200]) * 1500;

% 创建理论解
[X, Y, Z] = ndgrid(1:200, 1:200, 1:200);
T_theory = (sqrt((X-SourcePoint(1)).^2 + (Y-SourcePoint(2)).^2 + (Z-SourcePoint(3)).^2)) ./ SpeedImage;

fprintf('测试配置:\n');
fprintf('- 网格尺寸: 200x200x200\n');
fprintf('- 源点位置: (%d, %d, %d)\n', SourcePoint(1), SourcePoint(2), SourcePoint(3));
fprintf('- 均匀速度: %.0f m/s\n', SpeedImage(1));
fprintf('- 总体素数: %d\n\n', numel(SpeedImage));

%% 方法1: 几何分析法
fprintf('=== 方法1: 几何分析法 ===\n');
tic;
try
    T_geometric = geometric_analysis_simple(SpeedImage, SourcePoint);
    time_geometric = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_geometric);
    error_geometric = calculate_errors(T_theory, T_geometric);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_geometric.L1, error_geometric.L2, error_geometric.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_geometric = []; time_geometric = NaN;
    error_geometric = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法2: DAG算法 (标准配置)
fprintf('\n=== 方法2: DAG算法 (标准) ===\n');
tic;
try
    T_dag_std = dag_shortest_path_3d_matlab(SpeedImage, SourcePoint, 6, 3);  % Z方向分层
    time_dag_std = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_dag_std);
    error_dag_std = calculate_errors(T_theory, T_dag_std);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dag_std.L1, error_dag_std.L2, error_dag_std.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dag_std = []; time_dag_std = NaN;
    error_dag_std = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法3: DAG算法 (高级配置) - 小网格测试
fprintf('\n=== 方法3: DAG算法 (高级) ===\n');
small_size = [100 100 100];
SpeedImage_small = ones(small_size) * 1500;
SourcePoint_small = [1; 1; 1];

[X_s, Y_s, Z_s] = ndgrid(1:small_size(1), 1:small_size(2), 1:small_size(3));
T_theory_small = (sqrt((X_s-SourcePoint_small(1)).^2 + (Y_s-SourcePoint_small(2)).^2 + (Z_s-SourcePoint_small(3)).^2)) ./ SpeedImage_small;

options_dag = struct();
options_dag.connectivity = 26;
options_dag.topo_strategy = 'layer_by_layer';
options_dag.optimization = 'basic';

tic;
try
    T_dag_adv_small = dag_shortest_path_3d_advanced(SpeedImage_small, SourcePoint_small, options_dag);
    time_dag_adv = toc;
    fprintf('✓ 成功 (100x100x100)，用时: %.4f 秒\n', time_dag_adv);
    error_dag_adv = calculate_errors(T_theory_small, T_dag_adv_small);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dag_adv.L1, error_dag_adv.L2, error_dag_adv.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dag_adv_small = []; time_dag_adv = NaN;
    error_dag_adv = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法4: Dial算法
fprintf('\n=== 方法4: Dial算法 ===\n');
tic;
try
    T_dial = dial_3d_matlab(SpeedImage, SourcePoint, 6, 1000);
    time_dial = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_dial);
    error_dial = calculate_errors(T_theory, T_dial);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dial.L1, error_dial.L2, error_dial.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dial = []; time_dial = NaN;
    error_dial = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法5: A*算法
fprintf('\n=== 方法5: A*算法 ===\n');
tic;
try
    T_astar = astar_3d_matlab(SpeedImage, SourcePoint, 6, 1.0);
    time_astar = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_astar);
    error_astar = calculate_errors(T_theory, T_astar);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_astar.L1, error_astar.L2, error_astar.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_astar = []; time_astar = NaN;
    error_astar = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法6: Dijkstra算法
fprintf('\n=== 方法6: Dijkstra算法 ===\n');
tic;
try
    T_dijkstra = dijkstra_3d_matlab(SpeedImage, SourcePoint, 6);
    time_dijkstra = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_dijkstra);
    error_dijkstra = calculate_errors(T_theory, T_dijkstra);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dijkstra.L1, error_dijkstra.L2, error_dijkstra.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dijkstra = []; time_dijkstra = NaN;
    error_dijkstra = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法7-10: MSFM3D的四种配置
fprintf('\n=== 方法7-10: MSFM3D算法 ===\n');

methods_msfm = {'FMM1', 'MSFM1', 'FMM2', 'MSFM2'};
params_msfm = {[false, false], [false, true], [true, false], [true, true]};
results_msfm = cell(1, 4);
times_msfm = zeros(1, 4);
errors_msfm = cell(1, 4);

for i = 1:4
    fprintf('方法%d - %s: ', i+6, methods_msfm{i});
    tic;
    try
        results_msfm{i} = msfm3d(SpeedImage, SourcePoint, params_msfm{i}(1), params_msfm{i}(2));
        times_msfm(i) = toc;
        fprintf('✓ 用时: %.4f 秒\n', times_msfm(i));
        errors_msfm{i} = calculate_errors(T_theory, results_msfm{i});
    catch ME
        fprintf('✗ 失败: %s\n', ME.message);
        results_msfm{i} = [];
        times_msfm(i) = NaN;
        errors_msfm{i} = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
    end
end

%% 结果汇总表
fprintf('\n=== 完整结果汇总 ===\n');
fprintf('%-20s %12s %12s %12s %12s %15s\n', '方法', 'L1误差', 'L2误差', 'L∞误差', '时间(秒)', '效率(节点/秒)');
fprintf('%s\n', repmat('-', 1, 95));

% 收集所有结果
all_methods = {};
all_errors = [];
all_times = [];
all_efficiency = [];

% 几何分析法
if ~isempty(T_geometric)
    all_methods{end+1} = '几何分析法';
    all_errors = [all_errors; error_geometric.L1, error_geometric.L2, error_geometric.Linf];
    all_times = [all_times; time_geometric];
    all_efficiency = [all_efficiency; numel(SpeedImage)/time_geometric];
end

% DAG标准
if ~isempty(T_dag_std)
    all_methods{end+1} = 'DAG标准';
    all_errors = [all_errors; error_dag_std.L1, error_dag_std.L2, error_dag_std.Linf];
    all_times = [all_times; time_dag_std];
    all_efficiency = [all_efficiency; numel(SpeedImage)/time_dag_std];
end

% DAG高级 (注意：使用小网格)
if ~isempty(T_dag_adv_small)
    all_methods{end+1} = 'DAG高级(小网格)';
    all_errors = [all_errors; error_dag_adv.L1, error_dag_adv.L2, error_dag_adv.Linf];
    all_times = [all_times; time_dag_adv];
    all_efficiency = [all_efficiency; numel(SpeedImage_small)/time_dag_adv];
end

% 其他算法
other_results = {T_dial, T_astar, T_dijkstra};
other_errors = {error_dial, error_astar, error_dijkstra};
other_times = [time_dial, time_astar, time_dijkstra];
other_names = {'Dial算法', 'A*算法', 'Dijkstra'};

for i = 1:length(other_results)
    if ~isempty(other_results{i})
        all_methods{end+1} = other_names{i};
        all_errors = [all_errors; other_errors{i}.L1, other_errors{i}.L2, other_errors{i}.Linf];
        all_times = [all_times; other_times(i)];
        all_efficiency = [all_efficiency; numel(SpeedImage)/other_times(i)];
    end
end

% MSFM方法
for i = 1:4
    if ~isempty(results_msfm{i})
        all_methods{end+1} = methods_msfm{i};
        all_errors = [all_errors; errors_msfm{i}.L1, errors_msfm{i}.L2, errors_msfm{i}.Linf];
        all_times = [all_times; times_msfm(i)];
        all_efficiency = [all_efficiency; numel(SpeedImage)/times_msfm(i)];
    end
end

% 显示结果表
for i = 1:length(all_methods)
    fprintf('%-20s %12.2e %12.2e %12.2e %12.4f %15.0f\n', ...
        all_methods{i}, all_errors(i,1), all_errors(i,2), all_errors(i,3), ...
        all_times(i), all_efficiency(i));
end

%% DAG算法特性分析
fprintf('\n=== DAG算法特性分析 ===\n');

% 与其他图算法比较
graph_algorithms = {'DAG标准', 'Dijkstra', 'A*算法', 'Dial算法'};
graph_results = {T_dag_std, T_dijkstra, T_astar, T_dial};
graph_times = [time_dag_std, time_dijkstra, time_astar, time_dial];

fprintf('图算法性能比较:\n');
valid_idx = find(~isnan(graph_times));
if length(valid_idx) > 1
    [~, fastest_idx] = min(graph_times(valid_idx));
    fastest_method = graph_algorithms{valid_idx(fastest_idx)};
    fprintf('最快图算法: %s\n', fastest_method);
    
    % 计算加速比
    for i = valid_idx
        if i ~= valid_idx(fastest_idx)
            speedup = graph_times(i) / graph_times(valid_idx(fastest_idx));
            fprintf('  %s 相对加速比: %.2fx\n', graph_algorithms{i}, speedup);
        end
    end
end

% DAG算法的拓扑特性
if ~isempty(T_dag_std)
    fprintf('\nDAG算法拓扑特性:\n');
    fprintf('  分层方向: Z方向\n');
    fprintf('  理论时间复杂度: O(V + E)\n');
    fprintf('  实际处理效率: %.0f 节点/秒\n', numel(SpeedImage)/time_dag_std);
    
    % 与Dijkstra比较
    if ~isempty(T_dijkstra)
        accuracy_diff = abs(mean(T_dag_std(:)) - mean(T_dijkstra(:)));
        fprintf('  与Dijkstra精度差异: %.6f\n', accuracy_diff);
    end
end

%% 算法分类分析
fprintf('\n=== 算法分类分析 ===\n');

fprintf('1. 解析方法:\n');
if ~isempty(T_geometric)
    fprintf('   几何分析法: %.4f秒, L1=%.2e\n', time_geometric, error_geometric.L1);
end

fprintf('\n2. 图论方法:\n');
graph_methods = {'DAG标准', 'Dijkstra', 'A*算法', 'Dial算法'};
graph_data = {[time_dag_std, error_dag_std], [time_dijkstra, error_dijkstra], ...
              [time_astar, error_astar], [time_dial, error_dial]};

for i = 1:length(graph_methods)
    if ~isnan(graph_data{i}(1))
        fprintf('   %s: %.4f秒, L1=%.2e\n', graph_methods{i}, graph_data{i}(1), graph_data{i}(2).L1);
    end
end

fprintf('\n3. 偏微分方程方法:\n');
for i = 1:4
    if ~isempty(results_msfm{i})
        fprintf('   %s: %.4f秒, L1=%.2e\n', methods_msfm{i}, times_msfm(i), errors_msfm{i}.L1);
    end
end

%% 应用场景推荐
fprintf('\n=== 应用场景推荐 ===\n');
fprintf('1. 实时应用: 几何分析法 (最快)\n');
fprintf('2. 分层介质: DAG算法 (天然适合)\n');
fprintf('3. 高精度需求: MSFM2 或 Dijkstra\n');
fprintf('4. 启发式搜索: A*算法\n');
fprintf('5. 整数权重: Dial算法\n');
fprintf('6. 复杂介质: MSFM2\n');
fprintf('7. 无环保证: DAG算法\n');

%% 内存和复杂度分析
fprintf('\n=== 复杂度分析 ===\n');
n = numel(SpeedImage);
fprintf('网格规模: %d 个体素\n', n);
fprintf('算法复杂度比较:\n');
fprintf('  几何分析法: O(n) 时间, O(1) 额外空间\n');
fprintf('  DAG算法: O(V+E) 时间, O(V) 额外空间\n');
fprintf('  Dijkstra: O((V+E)logV) 时间, O(V) 额外空间\n');
fprintf('  A*算法: O(b^d) 时间, O(b^d) 额外空间\n');
fprintf('  Dial算法: O(V+E+C) 时间, O(V+C) 额外空间\n');
fprintf('  MSFM: O(n log n) 时间, O(n) 额外空间\n');

%% 辅助函数
function errors = calculate_errors(reference, computed)
    diff = reference(:) - computed(:);
    errors.L1 = mean(abs(diff));
    errors.L2 = sqrt(mean(diff.^2));
    errors.Linf = max(abs(diff));
end
