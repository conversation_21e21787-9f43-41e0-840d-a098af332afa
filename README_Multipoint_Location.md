# MSFM3D 多点定位演示代码

基于您的单点定位代码，我创建了两个多点定位演示版本，用于测试MSFM3D算法在多个震源位置的定位性能。

## 📁 文件说明

### 1. **`test_msfm3d_multipoint_location.m`** - 完整版多点定位演示
- **10个测试震源**：覆盖不同深度和空间位置
- **完整模型**：100×100×50的详细3D速度模型
- **详细分析**：包含误差与深度、台站距离的关系分析
- **丰富可视化**：多个图形展示定位结果和误差特性

### 2. **`quick_msfm3d_multipoint_test.m`** - 快速版多点定位测试
- **10个测试震源**：同样的测试点配置
- **简化模型**：50×50×25的小尺寸模型
- **快速计算**：适合快速验证和调试
- **基本可视化**：核心结果展示

## 🎯 测试震源配置

### 空间分布设计
```matlab
test_sources = [
    25, 25, 12;   % 测试点1 - 浅层左下
    75, 25, 12;   % 测试点2 - 浅层右下  
    75, 75, 12;   % 测试点3 - 浅层右上
    25, 75, 12;   % 测试点4 - 浅层左上
    50, 50, 15;   % 测试点5 - 浅层中心
    35, 35, 25;   % 测试点6 - 中层
    65, 65, 25;   % 测试点7 - 中层
    45, 55, 35;   % 测试点8 - 深层
    55, 45, 35;   % 测试点9 - 深层
    50, 50, 30    % 测试点10 - 深层中心
];
```

### 设计原理
- **深度分层**：浅层(12-15)、中层(25)、深层(30-35)
- **空间覆盖**：四角、中心、对角线分布
- **几何多样性**：测试不同台站几何条件下的定位精度

## 🚀 主要功能

### 1. **3D速度模型**
- 分层速度结构（2000-6500 m/s）
- 横向速度变化和随机扰动
- 低速异常体模拟复杂地质结构
- 可调整的模型复杂度

### 2. **台站网络**
- 9个台站的优化布局
- 良好的方位角覆盖
- 地表台站配置

### 3. **多点定位算法**
- 逐个震源的独立定位
- 统一的搜索参数和噪声水平
- 系统性的误差分析

### 4. **统计分析**
- **定位误差统计**：平均值、标准差、RMS、最大/最小值
- **走时残差分析**：拟合质量评估
- **计算时间统计**：性能评估
- **相关性分析**：误差与深度、台站距离的关系

### 5. **可视化功能**
- **3D散点图**：所有震源和定位结果的三维展示
- **平面投影**：XY、XZ平面的投影分析
- **误差分布图**：各测试点的误差统计
- **关系分析图**：误差与各因素的关系
- **个案分析**：最大/最小误差点的详细对比

## 📊 预期结果

### 典型性能指标
- **平均定位误差**：2-5个网格单位
- **RMS误差**：3-6个网格单位
- **平均走时残差**：1-3毫秒
- **单点计算时间**：10-60秒

### 误差特性分析
1. **深度效应**：深层震源通常误差较大
2. **几何效应**：台站网络边缘的震源误差较大
3. **速度结构影响**：异常体附近可能增加定位难度

## 🛠️ 使用方法

### 快速开始
```matlab
% 1. 快速测试（推荐首次使用）
quick_msfm3d_multipoint_test

% 2. 完整分析
test_msfm3d_multipoint_location
```

### 参数调整

#### 模型参数
```matlab
% 完整版
nx = 100; ny = 100; nz = 50;  % 高分辨率

% 快速版  
nx = 50; ny = 50; nz = 25;    % 低分辨率，快速计算
```

#### 搜索参数
```matlab
% 完整版 - 高精度
search_step = 5;
search_range_x = 20:search_step:80;

% 快速版 - 快速搜索
search_step = 2;
search_range_x = 8:search_step:42;
```

#### 噪声水平
```matlab
noise_level = 0.002;  % 2ms标准差（完整版）
noise_level = 0.001;  % 1ms标准差（快速版）
```

## 📈 结果解读

### 输出信息
1. **实时进度**：每个测试点的定位过程
2. **统计摘要**：整体性能指标
3. **详细表格**：每个测试点的具体结果
4. **可视化图形**：多角度的结果展示

### 关键指标
- **定位误差**：真实位置与定位结果的欧氏距离
- **走时残差**：观测走时与理论走时的RMS差异
- **计算效率**：每个测试点的搜索时间

### 质量评估
- **误差 < 3个网格单位**：优秀
- **误差 3-5个网格单位**：良好  
- **误差 5-8个网格单位**：可接受
- **误差 > 8个网格单位**：需要改进

## 🔧 性能优化

### 提高计算速度
1. **使用快速版本**：`quick_msfm3d_multipoint_test.m`
2. **减小模型尺寸**：降低nx、ny、nz
3. **增大搜索步长**：提高search_step
4. **限制搜索范围**：缩小搜索区域

### 提高定位精度
1. **使用完整版本**：`test_msfm3d_multipoint_location.m`
2. **增加模型分辨率**：提高网格密度
3. **减小搜索步长**：更精细搜索
4. **优化台站布局**：改善几何覆盖

## 🔍 故障排除

### 常见问题
1. **MEX函数错误**：确保msfm3d.mexw64在路径中
2. **内存不足**：使用快速版本或减小模型
3. **计算时间过长**：调整搜索参数
4. **定位误差过大**：检查速度模型和台站配置

### 调试建议
1. 先运行快速版本验证基本功能
2. 逐步增加模型复杂度
3. 检查个别测试点的详细结果
4. 分析误差与各因素的关系

## 📚 扩展应用

### 实际数据应用
1. 替换为真实的3D速度模型
2. 使用实际台站坐标
3. 输入真实地震观测数据
4. 调整搜索范围适应研究区域

### 算法改进
1. 实现多尺度网格搜索
2. 添加P波和S波联合定位
3. 引入观测权重处理
4. 实现定位不确定性分析

这套多点定位演示代码将帮助您全面评估MSFM3D算法的定位性能，分析不同条件下的精度变化，为实际地震定位应用提供重要参考。
