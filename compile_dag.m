%% 编译DAG最短路径算法MEX文件
% 这个脚本用于编译dag_shortest_path_3d.c文件并进行全面测试

fprintf('正在编译DAG最短路径算法MEX文件...\n');

try
    % 编译MEX文件
    mex dag_shortest_path_3d.c
    fprintf('✓ 编译成功！\n');
    
    % 基础功能测试
    fprintf('\n=== 基础功能测试 ===\n');
    test_speed = ones([30, 30, 30]) * 1500;
    test_source = [15; 15; 15];
    
    % 测试不同连接性和分层方向
    connectivities = [6, 18, 26];
    layer_directions = [0, 1, 2, 3];  % 0=auto, 1=X, 2=Y, 3=Z
    direction_names = {'自动', 'X方向', 'Y方向', 'Z方向'};
    
    fprintf('测试不同配置...\n');
    for c = 1:length(connectivities)
        conn = connectivities(c);
        for d = 1:length(layer_directions)
            dir = layer_directions(d);
            
            fprintf('连接性=%d, 分层=%s: ', conn, direction_names{d});
            tic;
            try
                result = dag_shortest_path_3d(test_speed, test_source, conn, dir);
                elapsed = toc;
                
                if all(size(result) == [30, 30, 30])
                    fprintf('✓ 成功 (%.3f秒)\n', elapsed);
                else
                    fprintf('✗ 尺寸错误\n');
                end
            catch ME
                fprintf('✗ 失败: %s\n', ME.message);
            end
        end
    end
    
    % 精度验证测试
    fprintf('\n=== 精度验证测试 ===\n');
    [X, Y, Z] = ndgrid(1:30, 1:30, 1:30);
    theory = sqrt((X-15).^2 + (Y-15).^2 + (Z-15).^2) / 1500;
    
    % 测试不同分层方向对精度的影响
    fprintf('分层方向对精度的影响:\n');
    for d = 1:length(layer_directions)
        dir = layer_directions(d);
        result = dag_shortest_path_3d(test_speed, test_source, 6, dir);
        error = mean(abs(theory(:) - result(:)));
        fprintf('  %s: 平均误差 %.6f\n', direction_names{d}, error);
    end
    
    % 与MATLAB版本比较
    fprintf('\n=== 与MATLAB版本比较 ===\n');
    try
        result_c = dag_shortest_path_3d(test_speed, test_source, 6, 3);  % Z方向
        result_matlab = dag_shortest_path_3d_matlab(test_speed, test_source, 6, 3);
        
        diff_versions = mean(abs(result_c(:) - result_matlab(:)));
        fprintf('C版本与MATLAB版本差异: %.6f\n', diff_versions);
        
        if diff_versions < 1e-6
            fprintf('✓ C版本与MATLAB版本结果基本一致\n');
        else
            fprintf('⚠ C版本与MATLAB版本存在差异\n');
        end
    catch
        fprintf('MATLAB版本不可用，跳过比较\n');
    end
    
    % 拓扑排序验证
    fprintf('\n=== 拓扑排序验证 ===\n');
    
    % 创建分层速度场进行测试
    layered_speed = ones([20, 20, 20]) * 1000;
    layered_speed(:, :, 1:7) = 2000;   % 上层
    layered_speed(:, :, 8:14) = 1500;  % 中层
    layered_speed(:, :, 15:20) = 3000; % 下层
    
    layered_source = [10; 10; 1];  % 从顶层开始
    
    fprintf('测试分层介质中的DAG算法...\n');
    tic;
    result_layered = dag_shortest_path_3d(layered_speed, layered_source, 6, 3);  % Z方向分层
    elapsed_layered = toc;
    
    fprintf('分层介质测试: %.3f秒\n', elapsed_layered);
    
    % 检查结果的单调性（在Z方向应该大致单调）
    mean_times_by_layer = zeros(20, 1);
    for z = 1:20
        mean_times_by_layer(z) = mean(mean(result_layered(:, :, z)));
    end
    
    % 计算单调性指标
    monotonic_violations = sum(diff(mean_times_by_layer) < 0);
    fprintf('Z方向单调性违反次数: %d/19\n', monotonic_violations);
    
    if monotonic_violations <= 2
        fprintf('✓ 结果在Z方向基本单调，符合分层结构\n');
    else
        fprintf('⚠ 结果单调性较差，可能存在拓扑问题\n');
    end
    
    % 性能基准测试
    fprintf('\n=== 性能基准测试 ===\n');
    sizes = [20, 40, 60, 80];
    
    for s = 1:length(sizes)
        sz = sizes(s);
        test_vol = ones([sz, sz, sz]) * 1500;
        test_src = [round(sz/2); round(sz/2); 1];  % 从一角开始
        
        fprintf('\n网格尺寸: %dx%dx%d (%d个体素)\n', sz, sz, sz, sz^3);
        
        % 测试不同连接性的性能
        for c = 1:length(connectivities)
            conn = connectivities(c);
            fprintf('  连接性 %d: ', conn);
            
            tic;
            try
                result = dag_shortest_path_3d(test_vol, test_src, conn, 3);  % Z方向
                elapsed = toc;
                rate = sz^3 / elapsed;
                fprintf('%.3f秒 (%.0f体素/秒)\n', elapsed, rate);
            catch ME
                fprintf('失败 (%s)\n', ME.message);
            end
        end
    end
    
    % 分层方向性能影响测试
    fprintf('\n=== 分层方向性能影响测试 ===\n');
    test_vol = ones([50, 50, 50]) * 1500;
    test_src = [25; 25; 25];
    
    fprintf('测试不同分层方向对性能的影响:\n');
    
    for d = 1:length(layer_directions)
        dir = layer_directions(d);
        fprintf('%s: ', direction_names{d});
        
        tic;
        try
            result = dag_shortest_path_3d(test_vol, test_src, 6, dir);
            elapsed = toc;
            
            % 计算精度
            [X, Y, Z] = ndgrid(1:50, 1:50, 1:50);
            theory = sqrt((X-25).^2 + (Y-25).^2 + (Z-25).^2) / 1500;
            error = mean(abs(theory(:) - result(:)));
            
            fprintf('%.3f秒, 误差=%.6f\n', elapsed, error);
        catch ME
            fprintf('失败 (%s)\n', ME.message);
        end
    end
    
    % 与其他算法比较
    fprintf('\n=== 与其他算法比较 ===\n');
    comp_vol = ones([60, 60, 60]) * 1500;
    comp_src = [30; 30; 30];
    
    % DAG算法
    fprintf('DAG算法: ');
    tic;
    result_dag = dag_shortest_path_3d(comp_vol, comp_src, 6, 0);  % 自动分层
    time_dag = toc;
    fprintf('%.3f秒\n', time_dag);
    
    % 与其他算法比较 (如果可用)
    algorithms = {'dijkstra_3d', 'astar_3d', 'dial_3d'};
    algorithm_names = {'Dijkstra', 'A*', 'Dial'};
    algorithm_params = {[6], [6, 1.0], [6, 1000]};
    
    for a = 1:length(algorithms)
        if exist(algorithms{a}, 'file') == 3
            fprintf('%s算法: ', algorithm_names{a});
            tic;
            try
                switch a
                    case 1  % Dijkstra
                        result_other = dijkstra_3d(comp_vol, comp_src, algorithm_params{a}(1));
                    case 2  % A*
                        result_other = astar_3d(comp_vol, comp_src, algorithm_params{a}(1), algorithm_params{a}(2));
                    case 3  % Dial
                        result_other = dial_3d(comp_vol, comp_src, algorithm_params{a}(1), algorithm_params{a}(2));
                end
                time_other = toc;
                fprintf('%.3f秒\n', time_other);
                
                % 比较结果差异
                diff = mean(abs(result_dag(:) - result_other(:)));
                fprintf('  与DAG差异: %.6f\n', diff);
                
                % 速度比较
                if time_other > time_dag
                    speedup = time_other / time_dag;
                    fprintf('  DAG加速比: %.2fx\n', speedup);
                else
                    slowdown = time_dag / time_other;
                    fprintf('  DAG减速比: %.2fx\n', slowdown);
                end
            catch ME
                fprintf('失败 (%s)\n', ME.message);
            end
        else
            fprintf('%s算法MEX文件不可用\n', algorithm_names{a});
        end
    end
    
    % 环检测测试
    fprintf('\n=== 环检测测试 ===\n');
    
    % 创建可能产生环的复杂速度场
    complex_speed = ones([30, 30, 30]) * 1500;
    
    % 添加速度异常区域
    complex_speed(10:20, 10:20, 10:20) = 3000;  % 高速区域
    complex_speed(5:15, 5:15, 20:25) = 800;     % 低速区域
    
    complex_source = [15; 15; 15];
    
    fprintf('测试复杂速度场中的环检测...\n');
    
    for d = 1:length(layer_directions)
        dir = layer_directions(d);
        fprintf('%s分层: ', direction_names{d});
        
        tic;
        try
            result = dag_shortest_path_3d(complex_speed, complex_source, 18, dir);
            elapsed = toc;
            
            % 检查结果的合理性
            finite_ratio = sum(isfinite(result(:))) / numel(result);
            fprintf('%.3f秒, 有限值比例: %.1f%%\n', elapsed, finite_ratio * 100);
        catch ME
            fprintf('失败 (%s)\n', ME.message);
        end
    end
    
    fprintf('\n✓ DAG最短路径算法MEX文件测试完成！\n');
    
catch ME
    fprintf('✗ 编译或测试失败: %s\n', ME.message);
    fprintf('\n可能的解决方案:\n');
    fprintf('1. 确保已安装MATLAB编译器 (运行: mex -setup)\n');
    fprintf('2. 检查dag_shortest_path_3d.c文件是否存在\n');
    fprintf('3. 确保C编译器已正确配置\n');
    fprintf('4. 检查系统内存是否足够\n');
    fprintf('5. 确保拓扑排序实现正确\n');
end

%% 使用建议和最佳实践
fprintf('\n=== DAG算法使用建议 ===\n');
fprintf('1. 分层方向选择:\n');
fprintf('   - 自动检测: 适合未知结构\n');
fprintf('   - X/Y/Z方向: 适合已知分层结构\n');
fprintf('   - 选择与主要速度变化方向一致的分层\n\n');

fprintf('2. 连接性选择:\n');
fprintf('   - 6-连通: 最快，适合规则分层\n');
fprintf('   - 18-连通: 平衡精度和速度\n');
fprintf('   - 26-连通: 最高精度，适合复杂结构\n\n');

fprintf('3. 适用场景:\n');
fprintf('   - 分层介质: 理想选择\n');
fprintf('   - 地质模型: 天然分层结构\n');
fprintf('   - 无环保证: 拓扑排序保证\n');
fprintf('   - 大规模计算: 线性时间复杂度\n\n');

fprintf('4. 性能优化:\n');
fprintf('   - 选择合适的分层方向\n');
fprintf('   - 避免产生大量环的配置\n');
fprintf('   - 考虑使用高级版本的优化功能\n');
fprintf('   - 对于均匀介质，几何分析法更快\n');
