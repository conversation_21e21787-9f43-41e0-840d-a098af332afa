% 比较MSFM2D与Runge-Kutta射线追踪法的性能
function compare_msfm_vs_rk()
clear; clc;

fprintf('=== MSFM2D vs Runge-Kutta射线追踪法性能比较 ===\n\n');

% ===== 1. 设置测试参数 =====
test_sources = [
    100, 150;   % 测试点1
    200, 300;   % 测试点2
    350, 250;   % 测试点3
    150, 400;   % 测试点4
    300, 100    % 测试点5
    ];

% ===== 2. 建立相同的速度模型 =====
fprintf('建立速度模型...\n');
SpeedImage = create_velocity_model();
stations = [50, 50; 450, 50; 450, 450; 50, 450; 250, 100; 250, 400];
num_stations = size(stations, 1);

fprintf('速度模型大小: %d × %d\n', size(SpeedImage, 1), size(SpeedImage, 2));
fprintf('台站数量: %d\n', num_stations);

% ===== 3. 测试MSFM2D方法 =====
fprintf('\n=== 测试MSFM2D方法 ===\n');
msfm_results = struct();

if exist('msfm2d', 'file')
    try
        % 计算MSFM2D走时表
        fprintf('计算MSFM2D走时表...\n');
        msfm_start_time = tic;
        
        msfm_travel_tables = cell(num_stations, 1);
        for i = 1:num_stations
            fprintf('  台站 %d...', i);
            station_start = tic;
            msfm_travel_tables{i} = msfm2d(SpeedImage, stations(i,:)', true, true);
            station_time = toc(station_start);
            fprintf(' %.2f秒\n', station_time);
        end
        
        msfm_table_time = toc(msfm_start_time);
        fprintf('MSFM2D走时表计算完成，总用时: %.2f秒\n', msfm_table_time);
        
        % 执行MSFM2D定位测试
        fprintf('执行MSFM2D定位测试...\n');
        msfm_location_start = tic;
        
        msfm_errors = zeros(size(test_sources, 1), 1);
        msfm_estimated = zeros(size(test_sources));
        
        for i = 1:size(test_sources, 1)
            true_source = test_sources(i, :);
            observed_times = generate_observed_times(true_source, msfm_travel_tables);
            estimated_source = grid_search_location(observed_times, msfm_travel_tables);
            
            msfm_errors(i) = norm(true_source - estimated_source);
            msfm_estimated(i, :) = estimated_source;
        end
        
        msfm_location_time = toc(msfm_location_start);
        
        % 保存MSFM2D结果
        msfm_results.success = true;
        msfm_results.table_time = msfm_table_time;
        msfm_results.location_time = msfm_location_time;
        msfm_results.total_time = msfm_table_time + msfm_location_time;
        msfm_results.errors = msfm_errors;
        msfm_results.estimated = msfm_estimated;
        msfm_results.travel_tables = msfm_travel_tables;
        
        fprintf('MSFM2D测试完成:\n');
        fprintf('  走时表计算: %.2f秒\n', msfm_table_time);
        fprintf('  定位计算: %.2f秒\n', msfm_location_time);
        fprintf('  总用时: %.2f秒\n', msfm_results.total_time);
        fprintf('  平均误差: %.2f网格单位\n', mean(msfm_errors));
        
    catch ME
        fprintf('MSFM2D测试失败: %s\n', ME.message);
        msfm_results.success = false;
    end
else
    fprintf('MSFM2D函数不可用\n');
    msfm_results.success = false;
end

% ===== 4. 测试RK射线追踪方法 =====
fprintf('\n=== 测试Runge-Kutta射线追踪方法 ===\n');
rk_results = struct();

try
    % 计算RK走时表
    fprintf('计算RK射线追踪走时表...\n');
    rk_start_time = tic;
    
    rk_travel_tables = cell(num_stations, 1);
    for i = 1:num_stations
        fprintf('  台站 %d...', i);
        station_start = tic;
        
        try
            rk_travel_tables{i} = rk_raytracing2d(SpeedImage, stations(i,:)', true, true);
        catch
            % 如果RK函数失败，使用简化方法
            fprintf('(使用简化方法)');
            rk_travel_tables{i} = compute_travel_times_simple(SpeedImage, stations(i,:)');
        end
        
        station_time = toc(station_start);
        fprintf(' %.2f秒\n', station_time);
    end
    
    rk_table_time = toc(rk_start_time);
    fprintf('RK射线追踪走时表计算完成，总用时: %.2f秒\n', rk_table_time);
    
    % 执行RK定位测试
    fprintf('执行RK射线追踪定位测试...\n');
    rk_location_start = tic;
    
    rk_errors = zeros(size(test_sources, 1), 1);
    rk_estimated = zeros(size(test_sources));
    
    for i = 1:size(test_sources, 1)
        true_source = test_sources(i, :);
        observed_times = generate_observed_times(true_source, rk_travel_tables);
        estimated_source = grid_search_location(observed_times, rk_travel_tables);
        
        rk_errors(i) = norm(true_source - estimated_source);
        rk_estimated(i, :) = estimated_source;
    end
    
    rk_location_time = toc(rk_location_start);
    
    % 保存RK结果
    rk_results.success = true;
    rk_results.table_time = rk_table_time;
    rk_results.location_time = rk_location_time;
    rk_results.total_time = rk_table_time + rk_location_time;
    rk_results.errors = rk_errors;
    rk_results.estimated = rk_estimated;
    rk_results.travel_tables = rk_travel_tables;
    
    fprintf('RK射线追踪测试完成:\n');
    fprintf('  走时表计算: %.2f秒\n', rk_table_time);
    fprintf('  定位计算: %.2f秒\n', rk_location_time);
    fprintf('  总用时: %.2f秒\n', rk_results.total_time);
    fprintf('  平均误差: %.2f网格单位\n', mean(rk_errors));
    
catch ME
    fprintf('RK射线追踪测试失败: %s\n', ME.message);
    rk_results.success = false;
end

% ===== 5. 性能比较分析 =====
fprintf('\n=== 性能比较分析 ===\n');

if msfm_results.success && rk_results.success
    % 时间性能比较
    fprintf('时间性能比较:\n');
    fprintf('  MSFM2D总用时: %.2f秒\n', msfm_results.total_time);
    fprintf('  RK射线追踪总用时: %.2f秒\n', rk_results.total_time);
    
    if rk_results.total_time < msfm_results.total_time
        speedup = msfm_results.total_time / rk_results.total_time;
        fprintf('  RK射线追踪比MSFM2D快 %.2fx\n', speedup);
    else
        slowdown = rk_results.total_time / msfm_results.total_time;
        fprintf('  RK射线追踪比MSFM2D慢 %.2fx\n', slowdown);
    end
    
    % 精度比较
    fprintf('\n精度比较:\n');
    fprintf('  MSFM2D平均误差: %.3f网格单位\n', mean(msfm_results.errors));
    fprintf('  RK射线追踪平均误差: %.3f网格单位\n', mean(rk_results.errors));
    fprintf('  MSFM2D RMS误差: %.3f网格单位\n', sqrt(mean(msfm_results.errors.^2)));
    fprintf('  RK射线追踪RMS误差: %.3f网格单位\n', sqrt(mean(rk_results.errors.^2)));
    
    % 误差差异分析
    error_diff = abs(msfm_results.errors - rk_results.errors);
    fprintf('  误差差异: %.3f ± %.3f网格单位\n', mean(error_diff), std(error_diff));
    
    % 走时表比较
    fprintf('\n走时表比较:\n');
    for i = 1:num_stations
        travel_diff = abs(msfm_results.travel_tables{i} - rk_results.travel_tables{i});
        valid_idx = isfinite(travel_diff);
        if sum(valid_idx(:)) > 0
            fprintf('  台站%d走时差异: %.4f ± %.4f秒\n', i, ...
                   mean(travel_diff(valid_idx)), std(travel_diff(valid_idx)));
        end
    end
    
elseif msfm_results.success
    fprintf('只有MSFM2D测试成功\n');
    fprintf('MSFM2D结果: 平均误差 %.3f网格单位，用时 %.2f秒\n', ...
           mean(msfm_results.errors), msfm_results.total_time);
    
elseif rk_results.success
    fprintf('只有RK射线追踪测试成功\n');
    fprintf('RK射线追踪结果: 平均误差 %.3f网格单位，用时 %.2f秒\n', ...
           mean(rk_results.errors), rk_results.total_time);
    
else
    fprintf('两种方法都测试失败\n');
    return;
end

% ===== 6. 可视化比较结果 =====
fprintf('\n生成比较可视化...\n');
visualize_comparison(SpeedImage, stations, test_sources, msfm_results, rk_results);

fprintf('\n=== 比较分析完成 ===\n');
end

% 辅助函数
function SpeedImage = create_velocity_model()
% 创建与原始代码相同的速度模型
xx = 1:500;

% 地层边界插值
x2 = [0, 43, 85, 170, 260, 289, 347, 404, 468, 500];
y2 = [44, 50, 56, 55, 56, 55, 57, 53, 62, 65];
yy2 = round(spline(x2, y2, xx));

x3 = [0, 34, 55, 104, 137, 212, 296, 403, 482, 500];
y3 = [98, 94, 101, 100, 101, 109, 111, 118, 108, 109];
yy3 = round(spline(x3, y3, xx));

x4 = [0, 51, 85, 102, 192, 211, 238, 276, 310, 337, 351, 418, 476, 500];
y4 = [150, 160, 160, 155, 153, 160, 151, 151, 149, 153, 148, 152, 145, 148];
yy4 = round(spline(x4, y4, xx));

x5 = [0, 25,46, 128, 200, 294, 339, 378, 425, 465, 500];
y5 = [160,169, 174, 173, 171, 172, 173, 171, 172, 169, 167];
yy5 = round(spline(x5, y5, xx));

% 限制边界
yy2 = min(max(yy2, 1), 500);
yy3 = min(max(yy3, 1), 500);
yy4 = min(max(yy4, 1), 500);
yy5 = min(max(yy5, 1), 500);

% 创建分层速度模型
SpeedImage = zeros(500, 500);
for col = 1:500
    SpeedImage(1:yy2(col), col) = 1500;
    SpeedImage(yy2(col)+1:yy3(col), col) = 3000;
    SpeedImage(yy3(col)+1:yy4(col), col) = 4200;
    SpeedImage(yy4(col)+1:yy5(col), col) = 5400;
    SpeedImage(yy5(col)+1:end, col) = 6000;
end

% 添加低速异常体
SpeedImage(341:366, 340:350) = 340;
SpeedImage(341:366, 315:325) = 340;
SpeedImage(341:366, 285:295) = 340;
SpeedImage(341:366, 245:255) = 340;
SpeedImage(341:366, 220:230) = 340;
SpeedImage(341:366, 180:190) = 340;
SpeedImage(341:366, 150:160) = 340;
SpeedImage(341:366, 120:130) = 340;

SpeedImage(316:321, 355:360) = 340;
SpeedImage(316:321, 295:300) = 340;
SpeedImage(316:321, 230:235) = 340;
SpeedImage(316:321, 160:165) = 340;
SpeedImage(316:321, 105:110) = 340;

SpeedImage(316:321, 110:200) = 340;
SpeedImage(316:321, 195:200) = 340;
SpeedImage(316:321, 195:270) = 340;
SpeedImage(316:321, 265:270) = 340;
SpeedImage(316:321, 265:360) = 340;

SpeedImage = rot90(SpeedImage, 2);
end

function observed_times = generate_observed_times(true_source, travel_time_tables)
% 生成观测走时数据
num_stations = length(travel_time_tables);
observed_times = zeros(num_stations, 1);

for i = 1:num_stations
    if true_source(1) >= 1 && true_source(1) <= size(travel_time_tables{i}, 1) && ...
       true_source(2) >= 1 && true_source(2) <= size(travel_time_tables{i}, 2)
        observed_times(i) = travel_time_tables{i}(true_source(1), true_source(2));
    else
        observed_times(i) = inf;
    end
    
    % 添加噪声
    noise_level = 0.001;
    observed_times(i) = observed_times(i) + noise_level * randn();
    observed_times(i) = max(observed_times(i), 1e-6);
end
end

function T = compute_travel_times_simple(SpeedImage, source_pos)
% 简化的走时计算函数
[rows, cols] = size(SpeedImage);
T = inf(rows, cols);

source_x = round(source_pos(1));
source_y = round(source_pos(2));

source_x = max(1, min(rows, source_x));
source_y = max(1, min(cols, source_y));

T(source_x, source_y) = 0;

for i = 1:rows
    for j = 1:cols
        if i == source_x && j == source_y
            continue;
        end

        dx = i - source_x;
        dy = j - source_y;
        distance = sqrt(dx^2 + dy^2);

        num_points = max(abs(dx), abs(dy)) + 1;
        x_step = dx / (num_points - 1);
        y_step = dy / (num_points - 1);

        total_time = 0;
        for step = 1:num_points-1
            x_pos = source_x + step * x_step;
            y_pos = source_y + step * y_step;

            x_idx = max(1, min(rows, round(x_pos)));
            y_idx = max(1, min(cols, round(y_pos)));
            velocity = SpeedImage(x_idx, y_idx);

            segment_distance = sqrt(x_step^2 + y_step^2);
            total_time = total_time + segment_distance / velocity;
        end

        T(i, j) = total_time;
    end
end
end

function visualize_comparison(SpeedImage, stations, test_sources, msfm_results, rk_results)
% 可视化比较结果

figure('Name', 'MSFM2D vs RK射线追踪比较', 'Position', [50, 50, 1400, 900]);

if msfm_results.success && rk_results.success
    % 速度模型
    subplot(2, 4, 1);
    pcolor(SpeedImage); shading interp; colormap(jet); colorbar;
    hold on;
    plot(stations(:,2), stations(:,1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
    plot(test_sources(:,2), test_sources(:,1), '*r', 'MarkerSize', 12, 'LineWidth', 2);
    title('速度模型与测试点');
    xlabel('Y'); ylabel('X');

    % MSFM2D定位结果
    subplot(2, 4, 2);
    pcolor(SpeedImage); shading interp; colormap(jet); colorbar;
    hold on;
    plot(stations(:,2), stations(:,1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
    plot(test_sources(:,2), test_sources(:,1), '*r', 'MarkerSize', 12, 'LineWidth', 2);
    plot(msfm_results.estimated(:,2), msfm_results.estimated(:,1), 'ob', 'MarkerSize', 8, 'LineWidth', 2);

    % 绘制误差线
    for i = 1:size(test_sources, 1)
        plot([test_sources(i,2), msfm_results.estimated(i,2)], ...
             [test_sources(i,1), msfm_results.estimated(i,1)], 'b--', 'LineWidth', 1);
    end
    title('MSFM2D定位结果');
    xlabel('Y'); ylabel('X');

    % RK射线追踪定位结果
    subplot(2, 4, 3);
    pcolor(SpeedImage); shading interp; colormap(jet); colorbar;
    hold on;
    plot(stations(:,2), stations(:,1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
    plot(test_sources(:,2), test_sources(:,1), '*r', 'MarkerSize', 12, 'LineWidth', 2);
    plot(rk_results.estimated(:,2), rk_results.estimated(:,1), 'og', 'MarkerSize', 8, 'LineWidth', 2);

    % 绘制误差线
    for i = 1:size(test_sources, 1)
        plot([test_sources(i,2), rk_results.estimated(i,2)], ...
             [test_sources(i,1), rk_results.estimated(i,1)], 'g--', 'LineWidth', 1);
    end
    title('RK射线追踪定位结果');
    xlabel('Y'); ylabel('X');

    % 误差比较
    subplot(2, 4, 4);
    x_pos = 1:length(msfm_results.errors);
    bar_width = 0.35;
    bar(x_pos - bar_width/2, msfm_results.errors, bar_width, 'FaceColor', 'blue', 'DisplayName', 'MSFM2D');
    hold on;
    bar(x_pos + bar_width/2, rk_results.errors, bar_width, 'FaceColor', 'green', 'DisplayName', 'RK射线追踪');
    title('定位误差比较');
    xlabel('测试点'); ylabel('误差 (网格单位)');
    legend('Location', 'best');
    grid on;

    % 时间性能比较
    subplot(2, 4, 5);
    methods = {'MSFM2D', 'RK射线追踪'};
    times = [msfm_results.total_time, rk_results.total_time];
    bar(times, 'FaceColor', [0.7, 0.3, 0.9]);
    set(gca, 'XTickLabel', methods);
    title('计算时间比较');
    ylabel('时间 (秒)');
    grid on;

    % 走时表差异（选择第一个台站）
    subplot(2, 4, 6);
    travel_diff = abs(msfm_results.travel_tables{1} - rk_results.travel_tables{1});
    pcolor(travel_diff); shading interp; colorbar;
    title('走时表差异 (台站1)');
    xlabel('Y'); ylabel('X');

    % 精度统计比较
    subplot(2, 4, 7);
    stats_msfm = [mean(msfm_results.errors), std(msfm_results.errors), max(msfm_results.errors)];
    stats_rk = [mean(rk_results.errors), std(rk_results.errors), max(rk_results.errors)];

    x_stats = 1:3;
    bar_width = 0.35;
    bar(x_stats - bar_width/2, stats_msfm, bar_width, 'FaceColor', 'blue', 'DisplayName', 'MSFM2D');
    hold on;
    bar(x_stats + bar_width/2, stats_rk, bar_width, 'FaceColor', 'green', 'DisplayName', 'RK射线追踪');

    set(gca, 'XTickLabel', {'平均', '标准差', '最大'});
    title('误差统计比较');
    ylabel('误差 (网格单位)');
    legend('Location', 'best');
    grid on;

    % 散点图比较
    subplot(2, 4, 8);
    scatter(msfm_results.errors, rk_results.errors, 100, 'filled');
    hold on;
    max_error = max([msfm_results.errors; rk_results.errors]);
    plot([0, max_error], [0, max_error], 'r--', 'LineWidth', 2);
    xlabel('MSFM2D误差');
    ylabel('RK射线追踪误差');
    title('误差相关性');
    grid on;

    % 计算相关系数
    correlation = corrcoef(msfm_results.errors, rk_results.errors);
    text(0.1, 0.9, sprintf('相关系数: %.3f', correlation(1,2)), ...
         'Units', 'normalized', 'FontSize', 10, 'FontWeight', 'bold');

else
    % 如果只有一种方法成功，显示可用的结果
    if msfm_results.success
        subplot(1, 2, 1);
        pcolor(SpeedImage); shading interp; colormap(jet); colorbar;
        hold on;
        plot(stations(:,2), stations(:,1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
        plot(test_sources(:,2), test_sources(:,1), '*r', 'MarkerSize', 12, 'LineWidth', 2);
        plot(msfm_results.estimated(:,2), msfm_results.estimated(:,1), 'ob', 'MarkerSize', 8, 'LineWidth', 2);
        title('MSFM2D定位结果');

        subplot(1, 2, 2);
        bar(msfm_results.errors);
        title('MSFM2D定位误差');
        xlabel('测试点'); ylabel('误差 (网格单位)');

    elseif rk_results.success
        subplot(1, 2, 1);
        pcolor(SpeedImage); shading interp; colormap(jet); colorbar;
        hold on;
        plot(stations(:,2), stations(:,1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
        plot(test_sources(:,2), test_sources(:,1), '*r', 'MarkerSize', 12, 'LineWidth', 2);
        plot(rk_results.estimated(:,2), rk_results.estimated(:,1), 'og', 'MarkerSize', 8, 'LineWidth', 2);
        title('RK射线追踪定位结果');

        subplot(1, 2, 2);
        bar(rk_results.errors);
        title('RK射线追踪定位误差');
        xlabel('测试点'); ylabel('误差 (网格单位)');
    end
end

sgtitle('MSFM2D与Runge-Kutta射线追踪法性能比较', 'FontSize', 14, 'FontWeight', 'bold');
end

function best_location = grid_search_location(observed_times, travel_time_tables)
% 网格搜索定位
search_range_x = 50:5:450;
search_range_y = 50:5:450;

min_residual = inf;
best_location = [0, 0];

for i = 1:length(search_range_x)
    for j = 1:length(search_range_y)
        x = search_range_x(i);
        y = search_range_y(j);

        theoretical_times = zeros(length(travel_time_tables), 1);
        valid_times = true;
        
        for k = 1:length(travel_time_tables)
            if x >= 1 && x <= size(travel_time_tables{k}, 1) && ...
               y >= 1 && y <= size(travel_time_tables{k}, 2)
                theoretical_times(k) = travel_time_tables{k}(x, y);
                if ~isfinite(theoretical_times(k)) || theoretical_times(k) <= 0
                    valid_times = false;
                    break;
                end
            else
                valid_times = false;
                break;
            end
        end

        if valid_times
            residual = sqrt(mean((observed_times - theoretical_times).^2));
            if residual < min_residual
                min_residual = residual;
                best_location = [x, y];
            end
        end
    end
end
end
