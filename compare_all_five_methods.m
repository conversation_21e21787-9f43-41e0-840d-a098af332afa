%% 五种方法完整比较：MSFM3D vs 几何分析法 vs Dijkstra vs A* vs Dial
% 全面比较五种3D传播时间计算方法

clear; clc;

fprintf('=== 五种方法完整比较测试 ===\n');
fprintf('1. MSFM3D (快速行进方法)\n');
fprintf('2. 几何分析法 (直接计算)\n');
fprintf('3. <PERSON>jk<PERSON>算法 (图论最短路径)\n');
fprintf('4. A*算法 (启发式搜索)\n');
fprintf('5. Dial算法 (优化的Dijkstra)\n\n');

%% 测试参数设置
SourcePoint = [1; 1; 1];
SpeedImage = ones([200 200 200]) * 1500;

% 创建理论解
[X, Y, Z] = ndgrid(1:200, 1:200, 1:200);
T_theory = (sqrt((X-SourcePoint(1)).^2 + (Y-SourcePoint(2)).^2 + (Z-SourcePoint(3)).^2)) ./ SpeedImage;

fprintf('测试配置:\n');
fprintf('- 网格尺寸: 200x200x200\n');
fprintf('- 源点位置: (%d, %d, %d)\n', SourcePoint(1), SourcePoint(2), SourcePoint(3));
fprintf('- 均匀速度: %.0f m/s\n', SpeedImage(1));
fprintf('- 总体素数: %d\n\n', numel(SpeedImage));

%% 方法1: 几何分析法
fprintf('=== 方法1: 几何分析法 ===\n');
tic;
try
    T_geometric = geometric_analysis_simple(SpeedImage, SourcePoint);
    time_geometric = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_geometric);
    error_geometric = calculate_errors(T_theory, T_geometric);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_geometric.L1, error_geometric.L2, error_geometric.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_geometric = []; time_geometric = NaN;
    error_geometric = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法2: Dial算法 (标准配置)
fprintf('\n=== 方法2: Dial算法 (标准) ===\n');
tic;
try
    T_dial_std = dial_3d_matlab(SpeedImage, SourcePoint, 6, 1000);
    time_dial_std = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_dial_std);
    error_dial_std = calculate_errors(T_theory, T_dial_std);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dial_std.L1, error_dial_std.L2, error_dial_std.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dial_std = []; time_dial_std = NaN;
    error_dial_std = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法3: Dial算法 (优化配置) - 小网格测试
fprintf('\n=== 方法3: Dial算法 (优化) ===\n');
small_size = [100 100 100];
SpeedImage_small = ones(small_size) * 1500;
SourcePoint_small = [1; 1; 1];

[X_s, Y_s, Z_s] = ndgrid(1:small_size(1), 1:small_size(2), 1:small_size(3));
T_theory_small = (sqrt((X_s-SourcePoint_small(1)).^2 + (Y_s-SourcePoint_small(2)).^2 + (Z_s-SourcePoint_small(3)).^2)) ./ SpeedImage_small;

options_dial = struct();
options_dial.connectivity = 26;
options_dial.adaptive_scaling = true;
options_dial.memory_efficient = true;

tic;
try
    T_dial_opt_small = dial_3d_optimized(SpeedImage_small, SourcePoint_small, options_dial);
    time_dial_opt = toc;
    fprintf('✓ 成功 (100x100x100)，用时: %.4f 秒\n', time_dial_opt);
    error_dial_opt = calculate_errors(T_theory_small, T_dial_opt_small);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dial_opt.L1, error_dial_opt.L2, error_dial_opt.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dial_opt_small = []; time_dial_opt = NaN;
    error_dial_opt = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法4: A*算法
fprintf('\n=== 方法4: A*算法 ===\n');
tic;
try
    T_astar = astar_3d_matlab(SpeedImage, SourcePoint, 6, 1.0);
    time_astar = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_astar);
    error_astar = calculate_errors(T_theory, T_astar);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_astar.L1, error_astar.L2, error_astar.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_astar = []; time_astar = NaN;
    error_astar = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法5: Dijkstra算法
fprintf('\n=== 方法5: Dijkstra算法 ===\n');
tic;
try
    T_dijkstra = dijkstra_3d_matlab(SpeedImage, SourcePoint, 6);
    time_dijkstra = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_dijkstra);
    error_dijkstra = calculate_errors(T_theory, T_dijkstra);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dijkstra.L1, error_dijkstra.L2, error_dijkstra.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dijkstra = []; time_dijkstra = NaN;
    error_dijkstra = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法6-9: MSFM3D的四种配置
fprintf('\n=== 方法6-9: MSFM3D算法 ===\n');

methods_msfm = {'FMM1', 'MSFM1', 'FMM2', 'MSFM2'};
params_msfm = {[false, false], [false, true], [true, false], [true, true]};
results_msfm = cell(1, 4);
times_msfm = zeros(1, 4);
errors_msfm = cell(1, 4);

for i = 1:4
    fprintf('方法%d - %s: ', i+5, methods_msfm{i});
    tic;
    try
        results_msfm{i} = msfm3d(SpeedImage, SourcePoint, params_msfm{i}(1), params_msfm{i}(2));
        times_msfm(i) = toc;
        fprintf('✓ 用时: %.4f 秒\n', times_msfm(i));
        errors_msfm{i} = calculate_errors(T_theory, results_msfm{i});
    catch ME
        fprintf('✗ 失败: %s\n', ME.message);
        results_msfm{i} = [];
        times_msfm(i) = NaN;
        errors_msfm{i} = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
    end
end

%% 结果汇总表
fprintf('\n=== 完整结果汇总 ===\n');
fprintf('%-20s %12s %12s %12s %12s %15s\n', '方法', 'L1误差', 'L2误差', 'L∞误差', '时间(秒)', '效率(节点/秒)');
fprintf('%s\n', repmat('-', 1, 90));

% 收集所有结果
all_methods = {};
all_errors = [];
all_times = [];
all_efficiency = [];

% 几何分析法
if ~isempty(T_geometric)
    all_methods{end+1} = '几何分析法';
    all_errors = [all_errors; error_geometric.L1, error_geometric.L2, error_geometric.Linf];
    all_times = [all_times; time_geometric];
    all_efficiency = [all_efficiency; numel(SpeedImage)/time_geometric];
end

% Dial标准
if ~isempty(T_dial_std)
    all_methods{end+1} = 'Dial标准';
    all_errors = [all_errors; error_dial_std.L1, error_dial_std.L2, error_dial_std.Linf];
    all_times = [all_times; time_dial_std];
    all_efficiency = [all_efficiency; numel(SpeedImage)/time_dial_std];
end

% Dial优化 (注意：使用小网格)
if ~isempty(T_dial_opt_small)
    all_methods{end+1} = 'Dial优化(小网格)';
    all_errors = [all_errors; error_dial_opt.L1, error_dial_opt.L2, error_dial_opt.Linf];
    all_times = [all_times; time_dial_opt];
    all_efficiency = [all_efficiency; numel(SpeedImage_small)/time_dial_opt];
end

% A*
if ~isempty(T_astar)
    all_methods{end+1} = 'A*算法';
    all_errors = [all_errors; error_astar.L1, error_astar.L2, error_astar.Linf];
    all_times = [all_times; time_astar];
    all_efficiency = [all_efficiency; numel(SpeedImage)/time_astar];
end

% Dijkstra
if ~isempty(T_dijkstra)
    all_methods{end+1} = 'Dijkstra';
    all_errors = [all_errors; error_dijkstra.L1, error_dijkstra.L2, error_dijkstra.Linf];
    all_times = [all_times; time_dijkstra];
    all_efficiency = [all_efficiency; numel(SpeedImage)/time_dijkstra];
end

% MSFM方法
for i = 1:4
    if ~isempty(results_msfm{i})
        all_methods{end+1} = methods_msfm{i};
        all_errors = [all_errors; errors_msfm{i}.L1, errors_msfm{i}.L2, errors_msfm{i}.Linf];
        all_times = [all_times; times_msfm(i)];
        all_efficiency = [all_efficiency; numel(SpeedImage)/times_msfm(i)];
    end
end

% 显示结果表
for i = 1:length(all_methods)
    fprintf('%-20s %12.2e %12.2e %12.2e %12.4f %15.0f\n', ...
        all_methods{i}, all_errors(i,1), all_errors(i,2), all_errors(i,3), ...
        all_times(i), all_efficiency(i));
end

%% 算法特性分析
fprintf('\n=== 算法特性分析 ===\n');

% 精度排序
if ~isempty(all_errors)
    [~, precision_rank] = sort(all_errors(:,1));
    fprintf('精度排序 (按L1误差):\n');
    for i = 1:min(5, length(precision_rank))
        idx = precision_rank(i);
        fprintf('  %d. %s: L1=%.2e\n', i, all_methods{idx}, all_errors(idx,1));
    end
end

% 速度排序
if ~isempty(all_times)
    [~, speed_rank] = sort(all_times);
    fprintf('\n速度排序 (按计算时间):\n');
    for i = 1:min(5, length(speed_rank))
        idx = speed_rank(i);
        fprintf('  %d. %s: %.4f秒\n', i, all_methods{idx}, all_times(idx));
    end
end

% 效率排序
if ~isempty(all_efficiency)
    [~, efficiency_rank] = sort(all_efficiency, 'descend');
    fprintf('\n效率排序 (节点/秒):\n');
    for i = 1:min(5, length(efficiency_rank))
        idx = efficiency_rank(i);
        fprintf('  %d. %s: %.0f节点/秒\n', i, all_methods{idx}, all_efficiency(idx));
    end
end

%% Dial算法特性分析
fprintf('\n=== Dial算法特性分析 ===\n');
if ~isempty(T_dial_std) && ~isempty(T_dijkstra)
    % 与Dijkstra比较
    diff_dial_dijkstra = mean(abs(T_dial_std(:) - T_dijkstra(:)));
    speedup_dial = time_dijkstra / time_dial_std;
    fprintf('Dial vs Dijkstra:\n');
    fprintf('  结果差异: %.6f\n', diff_dial_dijkstra);
    fprintf('  速度提升: %.2fx\n', speedup_dial);
end

if ~isempty(T_astar) && ~isempty(T_dial_std)
    % 与A*比较
    diff_dial_astar = mean(abs(T_dial_std(:) - T_astar(:)));
    speedup_vs_astar = time_astar / time_dial_std;
    fprintf('\nDial vs A*:\n');
    fprintf('  结果差异: %.6f\n', diff_dial_astar);
    fprintf('  速度比较: %.2fx\n', speedup_vs_astar);
end

%% 量化效果分析
fprintf('\n=== 量化效果分析 ===\n');
if ~isempty(T_dial_std)
    % 分析量化误差
    quantization_error = mean(abs(T_theory(:) - T_dial_std(:)));
    relative_error = quantization_error / mean(T_theory(:));
    fprintf('Dial算法量化分析:\n');
    fprintf('  绝对量化误差: %.6f\n', quantization_error);
    fprintf('  相对量化误差: %.4f%%\n', relative_error * 100);
    
    % 建议最优量化因子
    optimal_scale = 1 / quantization_error * 1e-6;  % 目标微秒级精度
    fprintf('  建议量化因子: %.0f (当前: 1000)\n', optimal_scale);
end

%% 应用建议
fprintf('\n=== 应用建议 ===\n');
fprintf('1. 实时应用: 几何分析法 (最快，均匀介质最准)\n');
fprintf('2. 高精度需求: MSFM2 或 Dijkstra (理论最优)\n');
fprintf('3. 平衡性能: Dial算法 (比Dijkstra快，精度相近)\n');
fprintf('4. 启发式搜索: A*算法 (有方向性的搜索)\n');
fprintf('5. 复杂介质: MSFM2 (专为波传播设计)\n');
fprintf('6. 整数权重: Dial算法 (专门优化)\n');

%% 内存使用分析
fprintf('\n=== 内存使用估算 ===\n');
base_memory = numel(SpeedImage) * 8 / 1024^2;  % MB
fprintf('基础内存 (速度场): %.1f MB\n', base_memory);
fprintf('各算法额外内存需求:\n');
fprintf('  几何分析法: ~%.1f MB (最少)\n', base_memory);
fprintf('  Dial算法: ~%.1f MB (桶结构)\n', base_memory * 1.5);
fprintf('  A*算法: ~%.1f MB (开放/关闭集)\n', base_memory * 2);
fprintf('  Dijkstra: ~%.1f MB (优先队列)\n', base_memory * 2.5);
fprintf('  MSFM: ~%.1f MB (窄带列表)\n', base_memory * 1.2);

%% 辅助函数
function errors = calculate_errors(reference, computed)
    diff = reference(:) - computed(:);
    errors.L1 = mean(abs(diff));
    errors.L2 = sqrt(mean(diff.^2));
    errors.Linf = max(abs(diff));
end
