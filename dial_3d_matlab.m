function T = dial_3d_matlab(SpeedImage, SourcePoints, connectivity, scale_factor)
%DIAL_3D_MATLAB Dial算法的3D实现
% 使用Dial算法计算3D网格中的传播时间，这是Dijkstra算法的优化版本
% 专门用于整数权重或可量化权重的情况
%
% 输入:
%   SpeedImage - 3D速度场 [nx x ny x nz]
%   SourcePoints - 源点坐标 [3 x N] (1-based索引)
%   connectivity - 连接性: 6, 18, 或 26 (默认6)
%   scale_factor - 量化比例因子 (默认1000)
%
% 输出:
%   T - 传播时间场 [nx x ny x nz]

if nargin < 3, connectivity = 6; end
if nargin < 4, scale_factor = 1000; end

% 获取尺寸
[nx, ny, nz] = size(SpeedImage);
num_sources = size(SourcePoints, 2);

% 初始化
T = inf(nx, ny, nz);
quantized_dist = inf(nx, ny, nz);
visited = false(nx, ny, nz);

% 估计最大传播时间以确定桶的数量
max_distance = sqrt(nx^2 + ny^2 + nz^2);
min_speed = min(SpeedImage(:));
max_travel_time = max_distance / max(min_speed, 1e-8);
max_buckets = min(ceil(max_travel_time * scale_factor), 100000);

% 初始化桶结构
% 每个桶存储具有相同量化距离的节点
buckets = cell(max_buckets, 1);
current_bucket = 1;
max_bucket_used = 0;

% 定义邻居偏移
neighbors = get_neighbor_offsets(connectivity);
num_neighbors = size(neighbors, 1);

% 初始化源点
for s = 1:num_sources
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    if sx >= 1 && sx <= nx && sy >= 1 && sy <= ny && sz >= 1 && sz <= nz
        quantized_dist(sx, sy, sz) = 0;
        T(sx, sy, sz) = 0;
        
        % 添加到第一个桶
        buckets{1} = [buckets{1}; [sx, sy, sz]];
        max_bucket_used = max(max_bucket_used, 1);
    end
end

fprintf('开始Dial算法，网格大小: %dx%dx%d，量化因子: %.0f\n', nx, ny, nz, scale_factor);
fprintf('最大桶数: %d\n', max_buckets);

processed_count = 0;
total_nodes = nx * ny * nz;

% Dial算法主循环
tic;
while current_bucket <= max_bucket_used
    % 处理当前桶中的所有节点
    while ~isempty(buckets{current_bucket})
        % 从当前桶中取出一个节点
        current_nodes = buckets{current_bucket};
        current = current_nodes(1, :);
        buckets{current_bucket}(1, :) = [];
        
        cx = current(1);
        cy = current(2);
        cz = current(3);
        
        % 跳过已访问的节点
        if visited(cx, cy, cz)
            continue;
        end
        
        % 标记为已访问
        visited(cx, cy, cz) = true;
        processed_count = processed_count + 1;
        
        % 显示进度
        if mod(processed_count, 50000) == 0
            elapsed = toc;
            rate = processed_count / elapsed;
            remaining = (total_nodes - processed_count) / rate;
            fprintf('进度: %d/%d (%.1f%%), 当前桶: %d, 速度: %.0f节点/秒, 预计剩余: %.1f秒\n', ...
                processed_count, total_nodes, 100*processed_count/total_nodes, ...
                current_bucket, rate, remaining);
        end
        
        % 检查所有邻居
        for n = 1:num_neighbors
            nx_coord = cx + neighbors(n, 1);
            ny_coord = cy + neighbors(n, 2);
            nz_coord = cz + neighbors(n, 3);
            
            % 边界检查
            if nx_coord < 1 || nx_coord > nx || ...
               ny_coord < 1 || ny_coord > ny || ...
               nz_coord < 1 || nz_coord > nz
                continue;
            end
            
            % 跳过已访问的邻居
            if visited(nx_coord, ny_coord, nz_coord)
                continue;
            end
            
            % 计算量化的传播时间
            [travel_time, quantized_time] = calculate_quantized_travel_time(...
                SpeedImage, cx, cy, cz, nx_coord, ny_coord, nz_coord, scale_factor);
            
            new_quantized_dist = quantized_dist(cx, cy, cz) + quantized_time;
            
            % 如果找到更短路径
            if new_quantized_dist < quantized_dist(nx_coord, ny_coord, nz_coord)
                quantized_dist(nx_coord, ny_coord, nz_coord) = new_quantized_dist;
                T(nx_coord, ny_coord, nz_coord) = new_quantized_dist / scale_factor;
                
                % 计算目标桶索引
                bucket_idx = new_quantized_dist + 1;  % MATLAB索引从1开始
                
                if bucket_idx <= max_buckets
                    % 添加到相应的桶
                    buckets{bucket_idx} = [buckets{bucket_idx}; [nx_coord, ny_coord, nz_coord]];
                    max_bucket_used = max(max_bucket_used, bucket_idx);
                end
            end
        end
    end
    
    % 移动到下一个桶
    current_bucket = current_bucket + 1;
end

total_time = toc;
fprintf('Dial算法完成！\n');
fprintf('总用时: %.2f秒, 处理节点: %d, 平均速度: %.0f节点/秒\n', ...
    total_time, processed_count, processed_count/total_time);

end

function neighbors = get_neighbor_offsets(connectivity)
%GET_NEIGHBOR_OFFSETS 获取邻居偏移量

switch connectivity
    case 6  % 面邻居
        neighbors = [
            -1,  0,  0;   1,  0,  0;   0, -1,  0;
             0,  1,  0;   0,  0, -1;   0,  0,  1
        ];
        
    case 18  % 面+边邻居
        neighbors = [
            % 面邻居
            -1,  0,  0;  1,  0,  0;  0, -1,  0;  0,  1,  0;  0,  0, -1;  0,  0,  1;
            % 边邻居
            -1, -1,  0; -1,  1,  0;  1, -1,  0;  1,  1,  0;
            -1,  0, -1; -1,  0,  1;  1,  0, -1;  1,  0,  1;
             0, -1, -1;  0, -1,  1;  0,  1, -1;  0,  1,  1
        ];
        
    case 26  % 面+边+角邻居
        neighbors = [
            % 面邻居
            -1,  0,  0;  1,  0,  0;  0, -1,  0;  0,  1,  0;  0,  0, -1;  0,  0,  1;
            % 边邻居
            -1, -1,  0; -1,  1,  0;  1, -1,  0;  1,  1,  0;
            -1,  0, -1; -1,  0,  1;  1,  0, -1;  1,  0,  1;
             0, -1, -1;  0, -1,  1;  0,  1, -1;  0,  1,  1;
            % 角邻居
            -1, -1, -1; -1, -1,  1; -1,  1, -1; -1,  1,  1;
             1, -1, -1;  1, -1,  1;  1,  1, -1;  1,  1,  1
        ];
        
    otherwise
        error('连接性必须是 6, 18, 或 26');
end

end

function [travel_time, quantized_time] = calculate_quantized_travel_time(...
    SpeedImage, x1, y1, z1, x2, y2, z2, scale_factor)
%CALCULATE_QUANTIZED_TRAVEL_TIME 计算量化的传播时间

% 获取两点的速度
v1 = SpeedImage(x1, y1, z1);
v2 = SpeedImage(x2, y2, z2);

% 使用调和平均速度
v_avg = 2 / (1/max(v1, 1e-8) + 1/max(v2, 1e-8));

% 计算欧几里得距离
dx = x2 - x1;
dy = y2 - y1;
dz = z2 - z1;
distance = sqrt(dx^2 + dy^2 + dz^2);

% 计算传播时间
travel_time = distance / v_avg;

% 量化传播时间
quantized_time = round(travel_time * scale_factor);

end
