#include "mex.h"
#include "math.h"
#include <stdlib.h>
#include <stdbool.h>

/*
 * DAG Shortest Path Algorithm for 3D Distance Calculation
 * This function calculates travel times using DAG shortest path algorithm
 * with topological sorting for improved efficiency in layered structures.
 * 
 * T = dag_shortest_path_3d(F, SourcePoints, connectivity, layer_direction)
 * 
 * Inputs:
 *   F: The 3D speed/velocity image
 *   SourcePoints: A list of starting points [3 x N]
 *   connectivity: 6 (face neighbors), 18 (face+edge), or 26 (face+edge+corner)
 *   layer_direction: 0=auto, 1=X, 2=Y, 3=Z (direction of layering)
 * 
 * Outputs:
 *   T: Travel time image from SourcePoints to all pixels
 */

#define INF 1e10
#define EPS 1e-8

/* Node structure for DAG */
typedef struct {
    int x, y, z;
    int index;
    int layer_level;
    double distance;
} DAGNode;

/* Queue structure for topological sorting */
typedef struct {
    int *queue;
    int front;
    int rear;
    int capacity;
} TopoQueue;

/* Helper function to calculate 3D index */
int mindex3(int x, int y, int z, int sizx, int sizy) {
    return z*sizx*sizy + y*sizx + x;
}

/* Check if coordinates are within bounds */
bool inbounds3d(int x, int y, int z, const mwSize *dims) {
    return (x >= 0 && x < dims[0] && y >= 0 && y < dims[1] && z >= 0 && z < dims[2]);
}

/* Create topological queue */
TopoQueue* topo_queue_create(int capacity) {
    TopoQueue *queue = (TopoQueue*)malloc(sizeof(TopoQueue));
    queue->queue = (int*)malloc(capacity * sizeof(int));
    queue->front = 0;
    queue->rear = 0;
    queue->capacity = capacity;
    return queue;
}

/* Destroy topological queue */
void topo_queue_destroy(TopoQueue *queue) {
    free(queue->queue);
    free(queue);
}

/* Enqueue */
void topo_enqueue(TopoQueue *queue, int value) {
    queue->queue[queue->rear] = value;
    queue->rear = (queue->rear + 1) % queue->capacity;
}

/* Dequeue */
int topo_dequeue(TopoQueue *queue) {
    if (queue->front == queue->rear) return -1;
    int value = queue->queue[queue->front];
    queue->front = (queue->front + 1) % queue->capacity;
    return value;
}

/* Check if queue is empty */
bool topo_is_empty(TopoQueue *queue) {
    return queue->front == queue->rear;
}

/* Calculate layer level based on direction */
int calculate_layer_level(int x, int y, int z, int layer_direction, const mwSize *dims) {
    switch (layer_direction) {
        case 1: return x;  /* X direction */
        case 2: return y;  /* Y direction */
        case 3: return z;  /* Z direction */
        default: 
            /* Auto: use distance from origin */
            return (int)(sqrt(x*x + y*y + z*z));
    }
}

/* Determine optimal layer direction */
int determine_layer_direction(double *F, const mwSize *dims) {
    /* Analyze velocity variation in each direction */
    double var_x = 0, var_y = 0, var_z = 0;
    int samples = 100;
    
    /* Sample variance in X direction */
    for (int i = 0; i < samples && i < dims[0]-1; i++) {
        int idx = i * dims[0] * dims[1] / samples;
        double diff = F[idx + 1] - F[idx];
        var_x += diff * diff;
    }
    
    /* Sample variance in Y direction */
    for (int i = 0; i < samples && i < dims[1]-1; i++) {
        int idx = i * dims[0] / samples;
        double diff = F[idx + dims[0]] - F[idx];
        var_y += diff * diff;
    }
    
    /* Sample variance in Z direction */
    for (int i = 0; i < samples && i < dims[2]-1; i++) {
        int idx = i * dims[0] * dims[1] / samples;
        double diff = F[idx + dims[0]*dims[1]] - F[idx];
        var_z += diff * diff;
    }
    
    /* Return direction with maximum variation (most structured) */
    if (var_x >= var_y && var_x >= var_z) return 1;  /* X */
    if (var_y >= var_z) return 2;  /* Y */
    return 3;  /* Z */
}

/* Calculate travel time between adjacent voxels */
double calculate_travel_time(double *F, const mwSize *dims, 
                           int x1, int y1, int z1, 
                           int x2, int y2, int z2) {
    double v1 = F[mindex3(x1, y1, z1, dims[0], dims[1])];
    double v2 = F[mindex3(x2, y2, z2, dims[0], dims[1])];
    
    /* Use harmonic mean of velocities */
    double v_avg = 2.0 / (1.0/fmax(v1, EPS) + 1.0/fmax(v2, EPS));
    
    /* Calculate Euclidean distance */
    double dx = x2 - x1;
    double dy = y2 - y1;
    double dz = z2 - z1;
    double distance = sqrt(dx*dx + dy*dy + dz*dz);
    
    return distance / v_avg;
}

/* Get neighbor offsets */
void get_neighbors(int connectivity, int **neighbors, int *num_neighbors) {
    static int neighbors_6[6][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1}
    };
    
    static int neighbors_18[18][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1},
        {-1, -1, 0}, {-1, 1, 0}, {1, -1, 0}, {1, 1, 0},
        {-1, 0, -1}, {-1, 0, 1}, {1, 0, -1}, {1, 0, 1},
        {0, -1, -1}, {0, -1, 1}, {0, 1, -1}, {0, 1, 1}
    };
    
    static int neighbors_26[26][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1},
        {-1, -1, 0}, {-1, 1, 0}, {1, -1, 0}, {1, 1, 0},
        {-1, 0, -1}, {-1, 0, 1}, {1, 0, -1}, {1, 0, 1},
        {0, -1, -1}, {0, -1, 1}, {0, 1, -1}, {0, 1, 1},
        {-1, -1, -1}, {-1, -1, 1}, {-1, 1, -1}, {-1, 1, 1},
        {1, -1, -1}, {1, -1, 1}, {1, 1, -1}, {1, 1, 1}
    };
    
    switch (connectivity) {
        case 6:
            *neighbors = (int*)neighbors_6;
            *num_neighbors = 6;
            break;
        case 18:
            *neighbors = (int*)neighbors_18;
            *num_neighbors = 18;
            break;
        case 26:
            *neighbors = (int*)neighbors_26;
            *num_neighbors = 26;
            break;
        default:
            *neighbors = (int*)neighbors_6;
            *num_neighbors = 6;
            break;
    }
}

/* Check if edge creates a cycle (for DAG validation) */
bool creates_cycle(int from_layer, int to_layer, int layer_direction) {
    /* In a proper DAG, edges should only go to higher or equal layers */
    return to_layer < from_layer;
}

/* Main MEX function */
void mexFunction(int nlhs, mxArray *plhs[], int nrhs, const mxArray *prhs[]) {
    /* Input validation */
    if (nrhs < 2 || nrhs > 4) {
        mexErrMsgTxt("2 to 4 inputs required: F, SourcePoints, [connectivity], [layer_direction]");
    }
    if (nlhs != 1) {
        mexErrMsgTxt("One output required");
    }
    
    /* Get input data */
    double *F = (double*)mxGetPr(prhs[0]);
    double *SourcePoints = (double*)mxGetPr(prhs[1]);
    
    int connectivity = (nrhs > 2) ? (int)mxGetScalar(prhs[2]) : 6;
    int layer_direction = (nrhs > 3) ? (int)mxGetScalar(prhs[3]) : 0;
    
    /* Get dimensions */
    const mwSize *dims = mxGetDimensions(prhs[0]);
    if (mxGetNumberOfDimensions(prhs[0]) != 3) {
        mexErrMsgTxt("Speed image must be 3D");
    }
    
    const mwSize *sp_dims = mxGetDimensions(prhs[1]);
    if (sp_dims[0] != 3) {
        mexErrMsgTxt("SourcePoints must be a 3xN matrix");
    }
    
    int npixels = dims[0] * dims[1] * dims[2];
    int num_sources = sp_dims[1];
    
    /* Auto-determine layer direction if not specified */
    if (layer_direction == 0) {
        layer_direction = determine_layer_direction(F, dims);
    }
    
    /* Create output array */
    plhs[0] = mxCreateNumericArray(3, dims, mxDOUBLE_CLASS, mxREAL);
    double *T = mxGetPr(plhs[0]);
    
    /* Initialize arrays */
    int *in_degree = (int*)calloc(npixels, sizeof(int));
    bool *processed = (bool*)calloc(npixels, sizeof(bool));
    
    /* Initialize distances */
    for (int i = 0; i < npixels; i++) {
        T[i] = INF;
    }
    
    /* Calculate layer levels for all nodes */
    int *layer_levels = (int*)malloc(npixels * sizeof(int));
    int max_layer = 0;
    
    for (int z = 0; z < dims[2]; z++) {
        for (int y = 0; y < dims[1]; y++) {
            for (int x = 0; x < dims[0]; x++) {
                int idx = mindex3(x, y, z, dims[0], dims[1]);
                layer_levels[idx] = calculate_layer_level(x, y, z, layer_direction, dims);
                if (layer_levels[idx] > max_layer) {
                    max_layer = layer_levels[idx];
                }
            }
        }
    }
    
    /* Get neighbor configuration */
    int *neighbors;
    int num_neighbors;
    get_neighbors(connectivity, &neighbors, &num_neighbors);
    
    /* Calculate in-degrees for topological sorting */
    for (int z = 0; z < dims[2]; z++) {
        for (int y = 0; y < dims[1]; y++) {
            for (int x = 0; x < dims[0]; x++) {
                int current_idx = mindex3(x, y, z, dims[0], dims[1]);
                int current_layer = layer_levels[current_idx];
                
                for (int n = 0; n < num_neighbors; n++) {
                    int nx = x + neighbors[n*3 + 0];
                    int ny = y + neighbors[n*3 + 1];
                    int nz = z + neighbors[n*3 + 2];
                    
                    if (inbounds3d(nx, ny, nz, dims)) {
                        int neighbor_idx = mindex3(nx, ny, nz, dims[0], dims[1]);
                        int neighbor_layer = layer_levels[neighbor_idx];
                        
                        /* Only count edges that don't create cycles */
                        if (!creates_cycle(current_layer, neighbor_layer, layer_direction)) {
                            in_degree[neighbor_idx]++;
                        }
                    }
                }
            }
        }
    }
    
    /* Initialize source points */
    TopoQueue *queue = topo_queue_create(npixels);
    
    for (int s = 0; s < num_sources; s++) {
        int sx = (int)(SourcePoints[0 + s*3] - 1);
        int sy = (int)(SourcePoints[1 + s*3] - 1);
        int sz = (int)(SourcePoints[2 + s*3] - 1);
        
        if (inbounds3d(sx, sy, sz, dims)) {
            int idx = mindex3(sx, sy, sz, dims[0], dims[1]);
            T[idx] = 0.0;
            topo_enqueue(queue, idx);
        }
    }
    
    /* Add all nodes with zero in-degree to queue */
    for (int i = 0; i < npixels; i++) {
        if (in_degree[i] == 0 && T[i] == INF) {
            topo_enqueue(queue, i);
        }
    }
    
    /* DAG shortest path algorithm with topological sorting */
    int processed_count = 0;
    
    while (!topo_is_empty(queue)) {
        int current_idx = topo_dequeue(queue);
        if (current_idx == -1) break;
        
        processed[current_idx] = true;
        processed_count++;
        
        /* Convert linear index back to 3D coordinates */
        int z = current_idx / (dims[0] * dims[1]);
        int y = (current_idx % (dims[0] * dims[1])) / dims[0];
        int x = current_idx % dims[0];
        
        int current_layer = layer_levels[current_idx];
        
        /* Process all neighbors */
        for (int n = 0; n < num_neighbors; n++) {
            int nx = x + neighbors[n*3 + 0];
            int ny = y + neighbors[n*3 + 1];
            int nz = z + neighbors[n*3 + 2];
            
            if (!inbounds3d(nx, ny, nz, dims)) continue;
            
            int neighbor_idx = mindex3(nx, ny, nz, dims[0], dims[1]);
            int neighbor_layer = layer_levels[neighbor_idx];
            
            /* Skip edges that would create cycles */
            if (creates_cycle(current_layer, neighbor_layer, layer_direction)) continue;
            
            if (!processed[neighbor_idx]) {
                /* Calculate travel time */
                double travel_time = calculate_travel_time(F, dims, x, y, z, nx, ny, nz);
                double new_distance = T[current_idx] + travel_time;
                
                /* Update distance if shorter path found */
                if (new_distance < T[neighbor_idx]) {
                    T[neighbor_idx] = new_distance;
                }
                
                /* Decrease in-degree and add to queue if it becomes zero */
                in_degree[neighbor_idx]--;
                if (in_degree[neighbor_idx] == 0) {
                    topo_enqueue(queue, neighbor_idx);
                }
            }
        }
    }
    
    /* Cleanup */
    topo_queue_destroy(queue);
    free(in_degree);
    free(processed);
    free(layer_levels);
}
