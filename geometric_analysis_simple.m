function T = geometric_analysis_simple(SpeedImage, SourcePoints)
%GEOMETRIC_ANALYSIS_SIMPLE 简化版几何分析法
% 专门用于均匀或简单分层速度场的快速距离计算
%
% 输入:
%   SpeedImage - 3D速度场
%   SourcePoints - 源点坐标 [3 x N]
%
% 输出:
%   T - 传播时间场

% 获取尺寸信息
[nx, ny, nz] = size(SpeedImage);
num_sources = size(SourcePoints, 2);

% 初始化输出
T = inf(nx, ny, nz);

% 创建坐标网格
[X, Y, Z] = ndgrid(1:nx, 1:ny, 1:nz);

% 检查是否为均匀速度场
uniform_speed = SpeedImage(1,1,1);
is_uniform = all(SpeedImage(:) == uniform_speed);

if is_uniform
    fprintf('检测到均匀速度场，使用直接几何计算...\n');
    
    % 对每个源点计算距离
    for s = 1:num_sources
        sx = SourcePoints(1, s);
        sy = SourcePoints(2, s);
        sz = SourcePoints(3, s);
        
        % 计算欧几里得距离
        distances = sqrt((X - sx).^2 + (Y - sy).^2 + (Z - sz).^2);
        
        % 转换为传播时间
        travel_times = distances / uniform_speed;
        
        % 取最小值（多源点情况）
        T = min(T, travel_times);
    end
    
else
    fprintf('检测到非均匀速度场，使用简化射线追踪...\n');
    
    % 对每个网格点计算传播时间
    for i = 1:nx
        for j = 1:ny
            for k = 1:nz
                min_time = inf;
                
                % 对每个源点计算最短时间
                for s = 1:num_sources
                    sx = SourcePoints(1, s);
                    sy = SourcePoints(2, s);
                    sz = SourcePoints(3, s);
                    
                    % 使用简化射线追踪
                    travel_time = simple_ray_tracing(SpeedImage, ...
                        sx, sy, sz, i, j, k);
                    
                    min_time = min(min_time, travel_time);
                end
                
                T(i, j, k) = min_time;
            end
        end
        
        % 显示进度
        if mod(i, 20) == 0
            fprintf('进度: %d/%d\n', i, nx);
        end
    end
end

end

function travel_time = simple_ray_tracing(SpeedImage, sx, sy, sz, tx, ty, tz)
%SIMPLE_RAY_TRACING 简化射线追踪算法
% 使用直线路径和平均速度近似

% 计算路径向量
dx = tx - sx;
dy = ty - sy;
dz = tz - sz;
total_distance = sqrt(dx^2 + dy^2 + dz^2);

if total_distance < 1e-10
    travel_time = 0;
    return;
end

% 归一化方向向量
dx = dx / total_distance;
dy = dy / total_distance;
dz = dz / total_distance;

% 射线追踪参数
step_size = 0.5;  % 步长
num_steps = ceil(total_distance / step_size);
actual_step = total_distance / num_steps;

% 沿射线积分
travel_time = 0;
for step = 1:num_steps
    % 当前位置
    current_x = sx + dx * actual_step * (step - 0.5);
    current_y = sy + dy * actual_step * (step - 0.5);
    current_z = sz + dz * actual_step * (step - 0.5);
    
    % 获取当前位置的速度（使用最近邻插值）
    ix = max(1, min(size(SpeedImage, 1), round(current_x)));
    iy = max(1, min(size(SpeedImage, 2), round(current_y)));
    iz = max(1, min(size(SpeedImage, 3), round(current_z)));
    
    velocity = SpeedImage(ix, iy, iz);
    velocity = max(velocity, 1e-8);  % 避免除零
    
    % 累加传播时间
    travel_time = travel_time + actual_step / velocity;
end

end
