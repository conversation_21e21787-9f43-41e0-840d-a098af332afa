function T = dijkstra_3d_matlab(SpeedImage, SourcePoints, connectivity)
%DIJKSTRA_3D_MATLAB Dijkstra算法的3D实现
% 使用Dijkstra最短路径算法计算3D网格中的传播时间
%
% 输入:
%   SpeedImage - 3D速度场 [nx x ny x nz]
%   SourcePoints - 源点坐标 [3 x N] (1-based索引)
%   connectivity - 连接性: 6(面邻居), 18(面+边), 26(面+边+角) (默认6)
%
% 输出:
%   T - 传播时间场 [nx x ny x nz]

if nargin < 3
    connectivity = 6;
end

% 获取尺寸
[nx, ny, nz] = size(SpeedImage);
num_sources = size(SourcePoints, 2);

% 初始化距离数组
T = inf(nx, ny, nz);
visited = false(nx, ny, nz);

% 创建线性索引映射
linear_size = nx * ny * nz;

% 定义邻居偏移
neighbors = get_neighbor_offsets(connectivity);
num_neighbors = size(neighbors, 1);

% 初始化优先队列 (使用MATLAB的容器)
% 格式: [distance, x, y, z]
priority_queue = [];

% 设置源点
for s = 1:num_sources
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    if sx >= 1 && sx <= nx && sy >= 1 && sy <= ny && sz >= 1 && sz <= nz
        T(sx, sy, sz) = 0;
        priority_queue = [priority_queue; [0, sx, sy, sz]];
    end
end

fprintf('开始Dijkstra算法，网格大小: %dx%dx%d\n', nx, ny, nz);
processed_count = 0;

% Dijkstra主循环
while ~isempty(priority_queue)
    % 找到最小距离的节点
    [~, min_idx] = min(priority_queue(:, 1));
    current = priority_queue(min_idx, :);
    priority_queue(min_idx, :) = [];  % 从队列中移除
    
    cx = current(2);
    cy = current(3);
    cz = current(4);
    current_dist = current(1);
    
    % 跳过已访问的节点
    if visited(cx, cy, cz)
        continue;
    end
    
    % 标记为已访问
    visited(cx, cy, cz) = true;
    processed_count = processed_count + 1;
    
    % 显示进度
    if mod(processed_count, 10000) == 0
        fprintf('已处理 %d/%d 个节点 (%.1f%%)\n', ...
            processed_count, linear_size, 100*processed_count/linear_size);
    end
    
    % 检查所有邻居
    for n = 1:num_neighbors
        nx_coord = cx + neighbors(n, 1);
        ny_coord = cy + neighbors(n, 2);
        nz_coord = cz + neighbors(n, 3);
        
        % 检查边界
        if nx_coord < 1 || nx_coord > nx || ...
           ny_coord < 1 || ny_coord > ny || ...
           nz_coord < 1 || nz_coord > nz
            continue;
        end
        
        % 跳过已访问的邻居
        if visited(nx_coord, ny_coord, nz_coord)
            continue;
        end
        
        % 计算到邻居的传播时间
        travel_time = calculate_travel_time_matlab(SpeedImage, ...
            cx, cy, cz, nx_coord, ny_coord, nz_coord);
        new_distance = current_dist + travel_time;
        
        % 如果找到更短路径，更新距离
        if new_distance < T(nx_coord, ny_coord, nz_coord)
            T(nx_coord, ny_coord, nz_coord) = new_distance;
            
            % 添加到优先队列
            priority_queue = [priority_queue; [new_distance, nx_coord, ny_coord, nz_coord]];
        end
    end
end

fprintf('Dijkstra算法完成，共处理 %d 个节点\n', processed_count);

end

function neighbors = get_neighbor_offsets(connectivity)
%GET_NEIGHBOR_OFFSETS 获取邻居偏移量

switch connectivity
    case 6  % 面邻居 (6-连通)
        neighbors = [
            -1,  0,  0;   % 左
             1,  0,  0;   % 右
             0, -1,  0;   % 前
             0,  1,  0;   % 后
             0,  0, -1;   % 下
             0,  0,  1    % 上
        ];
        
    case 18  % 面+边邻居 (18-连通)
        neighbors = [
            % 面邻居
            -1,  0,  0;  1,  0,  0;  0, -1,  0;  0,  1,  0;  0,  0, -1;  0,  0,  1;
            % 边邻居
            -1, -1,  0; -1,  1,  0;  1, -1,  0;  1,  1,  0;
            -1,  0, -1; -1,  0,  1;  1,  0, -1;  1,  0,  1;
             0, -1, -1;  0, -1,  1;  0,  1, -1;  0,  1,  1
        ];
        
    case 26  % 面+边+角邻居 (26-连通)
        neighbors = [
            % 面邻居
            -1,  0,  0;  1,  0,  0;  0, -1,  0;  0,  1,  0;  0,  0, -1;  0,  0,  1;
            % 边邻居
            -1, -1,  0; -1,  1,  0;  1, -1,  0;  1,  1,  0;
            -1,  0, -1; -1,  0,  1;  1,  0, -1;  1,  0,  1;
             0, -1, -1;  0, -1,  1;  0,  1, -1;  0,  1,  1;
            % 角邻居
            -1, -1, -1; -1, -1,  1; -1,  1, -1; -1,  1,  1;
             1, -1, -1;  1, -1,  1;  1,  1, -1;  1,  1,  1
        ];
        
    otherwise
        error('连接性必须是 6, 18, 或 26');
end

end

function travel_time = calculate_travel_time_matlab(SpeedImage, x1, y1, z1, x2, y2, z2)
%CALCULATE_TRAVEL_TIME_MATLAB 计算两个相邻体素间的传播时间

% 获取两点的速度
v1 = SpeedImage(x1, y1, z1);
v2 = SpeedImage(x2, y2, z2);

% 使用调和平均速度
v_avg = 2 / (1/max(v1, 1e-8) + 1/max(v2, 1e-8));

% 计算欧几里得距离
dx = x2 - x1;
dy = y2 - y1;
dz = z2 - z1;
distance = sqrt(dx^2 + dy^2 + dz^2);

% 返回传播时间
travel_time = distance / v_avg;

end
