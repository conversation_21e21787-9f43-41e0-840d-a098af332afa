%% 七种方法完整比较：MSFM3D vs 几何分析法 vs Dijkstra vs A* vs Dial vs DAG vs Bellman-Ford
% 全面比较七种3D传播时间计算方法

clear; clc;

fprintf('=== 七种方法完整比较测试 ===\n');
fprintf('1. MSFM3D (快速行进方法)\n');
fprintf('2. 几何分析法 (直接计算)\n');
fprintf('3. Dijkstra算法 (图论最短路径)\n');
fprintf('4. A*算法 (启发式搜索)\n');
fprintf('5. Dial算法 (优化的Dijkstra)\n');
fprintf('6. DAG算法 (有向无环图最短路径)\n');
fprintf('7. Bellman-Ford算法 (负权重最短路径)\n\n');

%% 测试参数设置
SourcePoint = [1; 1; 1];
SpeedImage = ones([200 200 200]) * 1500;

% 创建理论解
[X, Y, Z] = ndgrid(1:200, 1:200, 1:200);
T_theory = (sqrt((X-SourcePoint(1)).^2 + (Y-SourcePoint(2)).^2 + (Z-SourcePoint(3)).^2)) ./ SpeedImage;

fprintf('测试配置:\n');
fprintf('- 网格尺寸: 200x200x200\n');
fprintf('- 源点位置: (%d, %d, %d)\n', SourcePoint(1), SourcePoint(2), SourcePoint(3));
fprintf('- 均匀速度: %.0f m/s\n', SpeedImage(1));
fprintf('- 总体素数: %d\n\n', numel(SpeedImage));

%% 方法1: 几何分析法
fprintf('=== 方法1: 几何分析法 ===\n');
tic;
try
    T_geometric = geometric_analysis_simple(SpeedImage, SourcePoint);
    time_geometric = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_geometric);
    error_geometric = calculate_errors(T_theory, T_geometric);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_geometric.L1, error_geometric.L2, error_geometric.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_geometric = []; time_geometric = NaN;
    error_geometric = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法2: Bellman-Ford算法 (标准)
fprintf('\n=== 方法2: Bellman-Ford算法 (标准) ===\n');
tic;
try
    T_bf_std = bellman_ford_3d_matlab(SpeedImage, SourcePoint, 6, 50);  % 限制迭代次数
    time_bf_std = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_bf_std);
    error_bf_std = calculate_errors(T_theory, T_bf_std);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_bf_std.L1, error_bf_std.L2, error_bf_std.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_bf_std = []; time_bf_std = NaN;
    error_bf_std = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法3: Bellman-Ford算法 (SPFA优化) - 小网格测试
fprintf('\n=== 方法3: Bellman-Ford算法 (SPFA优化) ===\n');
small_size = [100 100 100];
SpeedImage_small = ones(small_size) * 1500;
SourcePoint_small = [1; 1; 1];

[X_s, Y_s, Z_s] = ndgrid(1:small_size(1), 1:small_size(2), 1:small_size(3));
T_theory_small = (sqrt((X_s-SourcePoint_small(1)).^2 + (Y_s-SourcePoint_small(2)).^2 + (Z_s-SourcePoint_small(3)).^2)) ./ SpeedImage_small;

options_bf = struct();
options_bf.connectivity = 6;
options_bf.algorithm = 'spfa';
options_bf.max_iterations = 1000;

tic;
try
    T_bf_spfa_small = bellman_ford_3d_optimized(SpeedImage_small, SourcePoint_small, options_bf);
    time_bf_spfa = toc;
    fprintf('✓ 成功 (100x100x100)，用时: %.4f 秒\n', time_bf_spfa);
    error_bf_spfa = calculate_errors(T_theory_small, T_bf_spfa_small);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_bf_spfa.L1, error_bf_spfa.L2, error_bf_spfa.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_bf_spfa_small = []; time_bf_spfa = NaN;
    error_bf_spfa = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法4: DAG算法
fprintf('\n=== 方法4: DAG算法 ===\n');
tic;
try
    T_dag = dag_shortest_path_3d_matlab(SpeedImage, SourcePoint, 6, 3);
    time_dag = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_dag);
    error_dag = calculate_errors(T_theory, T_dag);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dag.L1, error_dag.L2, error_dag.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dag = []; time_dag = NaN;
    error_dag = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法5: Dial算法
fprintf('\n=== 方法5: Dial算法 ===\n');
tic;
try
    T_dial = dial_3d_matlab(SpeedImage, SourcePoint, 6, 1000);
    time_dial = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_dial);
    error_dial = calculate_errors(T_theory, T_dial);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dial.L1, error_dial.L2, error_dial.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dial = []; time_dial = NaN;
    error_dial = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法6: A*算法
fprintf('\n=== 方法6: A*算法 ===\n');
tic;
try
    T_astar = astar_3d_matlab(SpeedImage, SourcePoint, 6, 1.0);
    time_astar = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_astar);
    error_astar = calculate_errors(T_theory, T_astar);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_astar.L1, error_astar.L2, error_astar.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_astar = []; time_astar = NaN;
    error_astar = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法7: Dijkstra算法
fprintf('\n=== 方法7: Dijkstra算法 ===\n');
tic;
try
    T_dijkstra = dijkstra_3d_matlab(SpeedImage, SourcePoint, 6);
    time_dijkstra = toc;
    fprintf('✓ 成功，用时: %.4f 秒\n', time_dijkstra);
    error_dijkstra = calculate_errors(T_theory, T_dijkstra);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dijkstra.L1, error_dijkstra.L2, error_dijkstra.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dijkstra = []; time_dijkstra = NaN;
    error_dijkstra = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法8-11: MSFM3D的四种配置
fprintf('\n=== 方法8-11: MSFM3D算法 ===\n');

methods_msfm = {'FMM1', 'MSFM1', 'FMM2', 'MSFM2'};
params_msfm = {[false, false], [false, true], [true, false], [true, true]};
results_msfm = cell(1, 4);
times_msfm = zeros(1, 4);
errors_msfm = cell(1, 4);

for i = 1:4
    fprintf('方法%d - %s: ', i+7, methods_msfm{i});
    tic;
    try
        results_msfm{i} = msfm3d(SpeedImage, SourcePoint, params_msfm{i}(1), params_msfm{i}(2));
        times_msfm(i) = toc;
        fprintf('✓ 用时: %.4f 秒\n', times_msfm(i));
        errors_msfm{i} = calculate_errors(T_theory, results_msfm{i});
    catch ME
        fprintf('✗ 失败: %s\n', ME.message);
        results_msfm{i} = [];
        times_msfm(i) = NaN;
        errors_msfm{i} = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
    end
end

%% 结果汇总表
fprintf('\n=== 完整结果汇总 ===\n');
fprintf('%-25s %12s %12s %12s %12s %15s\n', '方法', 'L1误差', 'L2误差', 'L∞误差', '时间(秒)', '效率(节点/秒)');
fprintf('%s\n', repmat('-', 1, 100));

% 收集所有结果
all_methods = {};
all_errors = [];
all_times = [];
all_efficiency = [];

% 几何分析法
if ~isempty(T_geometric)
    all_methods{end+1} = '几何分析法';
    all_errors = [all_errors; error_geometric.L1, error_geometric.L2, error_geometric.Linf];
    all_times = [all_times; time_geometric];
    all_efficiency = [all_efficiency; numel(SpeedImage)/time_geometric];
end

% Bellman-Ford标准
if ~isempty(T_bf_std)
    all_methods{end+1} = 'Bellman-Ford标准';
    all_errors = [all_errors; error_bf_std.L1, error_bf_std.L2, error_bf_std.Linf];
    all_times = [all_times; time_bf_std];
    all_efficiency = [all_efficiency; numel(SpeedImage)/time_bf_std];
end

% Bellman-Ford SPFA (注意：使用小网格)
if ~isempty(T_bf_spfa_small)
    all_methods{end+1} = 'Bellman-Ford SPFA(小网格)';
    all_errors = [all_errors; error_bf_spfa.L1, error_bf_spfa.L2, error_bf_spfa.Linf];
    all_times = [all_times; time_bf_spfa];
    all_efficiency = [all_efficiency; numel(SpeedImage_small)/time_bf_spfa];
end

% 其他算法
other_results = {T_dag, T_dial, T_astar, T_dijkstra};
other_errors = {error_dag, error_dial, error_astar, error_dijkstra};
other_times = [time_dag, time_dial, time_astar, time_dijkstra];
other_names = {'DAG算法', 'Dial算法', 'A*算法', 'Dijkstra'};

for i = 1:length(other_results)
    if ~isempty(other_results{i})
        all_methods{end+1} = other_names{i};
        all_errors = [all_errors; other_errors{i}.L1, other_errors{i}.L2, other_errors{i}.Linf];
        all_times = [all_times; other_times(i)];
        all_efficiency = [all_efficiency; numel(SpeedImage)/other_times(i)];
    end
end

% MSFM方法
for i = 1:4
    if ~isempty(results_msfm{i})
        all_methods{end+1} = methods_msfm{i};
        all_errors = [all_errors; errors_msfm{i}.L1, errors_msfm{i}.L2, errors_msfm{i}.Linf];
        all_times = [all_times; times_msfm(i)];
        all_efficiency = [all_efficiency; numel(SpeedImage)/times_msfm(i)];
    end
end

% 显示结果表
for i = 1:length(all_methods)
    fprintf('%-25s %12.2e %12.2e %12.2e %12.4f %15.0f\n', ...
        all_methods{i}, all_errors(i,1), all_errors(i,2), all_errors(i,3), ...
        all_times(i), all_efficiency(i));
end

%% Bellman-Ford算法特性分析
fprintf('\n=== Bellman-Ford算法特性分析 ===\n');

% 与其他图算法比较
graph_algorithms = {'Bellman-Ford标准', 'Dijkstra', 'A*算法', 'Dial算法', 'DAG算法'};
graph_results = {T_bf_std, T_dijkstra, T_astar, T_dial, T_dag};
graph_times = [time_bf_std, time_dijkstra, time_astar, time_dial, time_dag];

fprintf('图算法性能比较:\n');
valid_idx = find(~isnan(graph_times));
if length(valid_idx) > 1
    [~, fastest_idx] = min(graph_times(valid_idx));
    fastest_method = graph_algorithms{valid_idx(fastest_idx)};
    fprintf('最快图算法: %s\n', fastest_method);
    
    % 计算相对性能
    for i = valid_idx
        if i ~= valid_idx(fastest_idx)
            ratio = graph_times(i) / graph_times(valid_idx(fastest_idx));
            fprintf('  %s 相对时间: %.2fx\n', graph_algorithms{i}, ratio);
        end
    end
end

% Bellman-Ford的收敛特性
if ~isempty(T_bf_std)
    fprintf('\nBellman-Ford算法特性:\n');
    fprintf('  理论时间复杂度: O(VE)\n');
    fprintf('  实际处理效率: %.0f 节点/秒\n', numel(SpeedImage)/time_bf_std);
    fprintf('  负环检测: 支持\n');
    fprintf('  适用场景: 负权重边、稳定性要求高\n');
    
    % 与Dijkstra比较
    if ~isempty(T_dijkstra)
        accuracy_diff = abs(mean(T_bf_std(:)) - mean(T_dijkstra(:)));
        time_ratio = time_bf_std / time_dijkstra;
        fprintf('  与Dijkstra精度差异: %.6f\n', accuracy_diff);
        fprintf('  与Dijkstra时间比: %.2fx\n', time_ratio);
    end
end

%% 算法分类和特性分析
fprintf('\n=== 算法分类和特性分析 ===\n');

fprintf('1. 解析方法:\n');
if ~isempty(T_geometric)
    fprintf('   几何分析法: %.4f秒, L1=%.2e (最快，均匀介质最准)\n', ...
        time_geometric, error_geometric.L1);
end

fprintf('\n2. 图论方法 (按复杂度排序):\n');
graph_methods = {'DAG算法', 'Dijkstra', 'A*算法', 'Dial算法', 'Bellman-Ford标准'};
graph_complexities = {'O(V+E)', 'O((V+E)logV)', 'O(b^d)', 'O(V+E+C)', 'O(VE)'};
graph_data = {[time_dag, error_dag], [time_dijkstra, error_dijkstra], ...
              [time_astar, error_astar], [time_dial, error_dial], [time_bf_std, error_bf_std]};

for i = 1:length(graph_methods)
    if ~isnan(graph_data{i}(1))
        fprintf('   %s (%s): %.4f秒, L1=%.2e\n', ...
            graph_methods{i}, graph_complexities{i}, graph_data{i}(1), graph_data{i}(2).L1);
    end
end

fprintf('\n3. 偏微分方程方法:\n');
for i = 1:4
    if ~isempty(results_msfm{i})
        fprintf('   %s: %.4f秒, L1=%.2e\n', methods_msfm{i}, times_msfm(i), errors_msfm{i}.L1);
    end
end

%% 应用场景推荐
fprintf('\n=== 应用场景推荐 ===\n');
fprintf('1. 实时应用: 几何分析法 (最快)\n');
fprintf('2. 负权重处理: Bellman-Ford (唯一支持)\n');
fprintf('3. 分层介质: DAG算法 (天然适合)\n');
fprintf('4. 高精度需求: MSFM2 或 Dijkstra\n');
fprintf('5. 启发式搜索: A*算法\n');
fprintf('6. 整数权重: Dial算法\n');
fprintf('7. 复杂介质: MSFM2\n');
fprintf('8. 稳定性要求: Bellman-Ford\n');

%% 收敛性和稳定性分析
fprintf('\n=== 收敛性和稳定性分析 ===\n');
fprintf('算法收敛保证:\n');
fprintf('  几何分析法: 直接计算，无收敛问题\n');
fprintf('  Dijkstra: 保证收敛到最优解\n');
fprintf('  A*: 启发式满足条件时保证最优\n');
fprintf('  Dial: 保证收敛，类似Dijkstra\n');
fprintf('  DAG: 拓扑排序保证收敛\n');
fprintf('  Bellman-Ford: 保证收敛，可检测负环\n');
fprintf('  MSFM: 数值稳定性依赖于实现\n');

%% 内存使用分析
fprintf('\n=== 内存使用分析 ===\n');
base_memory = numel(SpeedImage) * 8 / 1024^2;  % MB
fprintf('基础内存 (速度场): %.1f MB\n', base_memory);
fprintf('各算法额外内存需求:\n');
fprintf('  几何分析法: ~%.1f MB (最少)\n', base_memory);
fprintf('  Bellman-Ford: ~%.1f MB (边列表)\n', base_memory * 3);
fprintf('  DAG算法: ~%.1f MB (拓扑结构)\n', base_memory * 1.5);
fprintf('  Dial算法: ~%.1f MB (桶结构)\n', base_memory * 1.5);
fprintf('  A*算法: ~%.1f MB (开放/关闭集)\n', base_memory * 2);
fprintf('  Dijkstra: ~%.1f MB (优先队列)\n', base_memory * 2.5);
fprintf('  MSFM: ~%.1f MB (窄带列表)\n', base_memory * 1.2);

%% 辅助函数
function errors = calculate_errors(reference, computed)
    diff = reference(:) - computed(:);
    errors.L1 = mean(abs(diff));
    errors.L2 = sqrt(mean(diff.^2));
    errors.Linf = max(abs(diff));
end
