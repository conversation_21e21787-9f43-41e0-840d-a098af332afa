function T = astar_3d_advanced(SpeedImage, SourcePoints, options)
%ASTAR_3D_ADVANCED 高级A*算法3D实现
% 支持多种启发式函数和优化策略
%
% 输入:
%   SpeedImage - 3D速度场 [nx x ny x nz]
%   SourcePoints - 源点坐标 [3 x N] (1-based索引)
%   options - 选项结构体:
%     .connectivity - 连接性: 6, 18, 或 26 (默认6)
%     .heuristic_type - 启发式类型: 'euclidean', 'manhattan', 'octile', 'adaptive' (默认'euclidean')
%     .heuristic_weight - 启发式权重 (默认1.0)
%     .tie_breaking - 是否使用打破平局策略 (默认true)
%     .early_termination - 早期终止阈值 (默认inf，不使用)
%
% 输出:
%   T - 传播时间场 [nx x ny x nz]

% 默认选项
if nargin < 3, options = struct(); end
if ~isfield(options, 'connectivity'), options.connectivity = 6; end
if ~isfield(options, 'heuristic_type'), options.heuristic_type = 'euclidean'; end
if ~isfield(options, 'heuristic_weight'), options.heuristic_weight = 1.0; end
if ~isfield(options, 'tie_breaking'), options.tie_breaking = true; end
if ~isfield(options, 'early_termination'), options.early_termination = inf; end

% 获取尺寸
[nx, ny, nz] = size(SpeedImage);
num_sources = size(SourcePoints, 2);

% 初始化
T = inf(nx, ny, nz);
g_cost = inf(nx, ny, nz);
f_cost = inf(nx, ny, nz);
closed_set = false(nx, ny, nz);

% 预计算速度统计信息
speed_stats = calculate_speed_statistics(SpeedImage);

% 定义邻居偏移和距离
[neighbors, neighbor_distances] = get_advanced_neighbors(options.connectivity);
num_neighbors = size(neighbors, 1);

% 开放集合 (优先队列)
% 格式: [f_cost, tie_breaker, g_cost, x, y, z]
open_set = [];

% 初始化源点
for s = 1:num_sources
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    if sx >= 1 && sx <= nx && sy >= 1 && sy <= ny && sz >= 1 && sz <= nz
        g_cost(sx, sy, sz) = 0;
        h_cost = calculate_advanced_heuristic(sx, sy, sz, SourcePoints, ...
            speed_stats, options);
        f_cost(sx, sy, sz) = h_cost;
        T(sx, sy, sz) = 0;
        
        tie_breaker = options.tie_breaking ? calculate_tie_breaker(sx, sy, sz, SourcePoints) : 0;
        open_set = [open_set; [h_cost, tie_breaker, 0, sx, sy, sz]];
    end
end

fprintf('开始高级A*算法\n');
fprintf('配置: 连接性=%d, 启发式=%s, 权重=%.2f\n', ...
    options.connectivity, options.heuristic_type, options.heuristic_weight);

processed_count = 0;
total_nodes = nx * ny * nz;

% A*主循环
tic;
while ~isempty(open_set)
    % 按f_cost排序，如果相等则按tie_breaker排序
    if options.tie_breaking
        [~, sort_idx] = sortrows(open_set, [1, 2]);
    else
        [~, sort_idx] = sort(open_set(:, 1));
    end
    
    current = open_set(sort_idx(1), :);
    open_set(sort_idx(1), :) = [];
    
    cx = current(4);
    cy = current(5);
    cz = current(6);
    current_g = current(3);
    
    % 跳过已处理的节点
    if closed_set(cx, cy, cz)
        continue;
    end
    
    % 早期终止检查
    if current_g >= options.early_termination
        fprintf('达到早期终止条件，停止搜索\n');
        break;
    end
    
    % 添加到关闭集合
    closed_set(cx, cy, cz) = true;
    processed_count = processed_count + 1;
    
    % 显示进度
    if mod(processed_count, 30000) == 0
        elapsed = toc;
        rate = processed_count / elapsed;
        fprintf('进度: %d/%d (%.1f%%), 速度: %.0f节点/秒\n', ...
            processed_count, total_nodes, 100*processed_count/total_nodes, rate);
    end
    
    % 检查所有邻居
    for n = 1:num_neighbors
        nx_coord = cx + neighbors(n, 1);
        ny_coord = cy + neighbors(n, 2);
        nz_coord = cz + neighbors(n, 3);
        
        % 边界检查
        if nx_coord < 1 || nx_coord > nx || ...
           ny_coord < 1 || ny_coord > ny || ...
           nz_coord < 1 || nz_coord > nz
            continue;
        end
        
        % 跳过已关闭的邻居
        if closed_set(nx_coord, ny_coord, nz_coord)
            continue;
        end
        
        % 计算到邻居的传播时间
        travel_time = calculate_adaptive_travel_time(SpeedImage, ...
            cx, cy, cz, nx_coord, ny_coord, nz_coord, neighbor_distances(n));
        tentative_g = current_g + travel_time;
        
        % 如果找到更好的路径
        if tentative_g < g_cost(nx_coord, ny_coord, nz_coord)
            g_cost(nx_coord, ny_coord, nz_coord) = tentative_g;
            
            % 计算启发式代价
            h_cost = calculate_advanced_heuristic(nx_coord, ny_coord, nz_coord, ...
                SourcePoints, speed_stats, options);
            f_total = tentative_g + h_cost;
            f_cost(nx_coord, ny_coord, nz_coord) = f_total;
            
            T(nx_coord, ny_coord, nz_coord) = tentative_g;
            
            % 计算打破平局值
            tie_breaker = options.tie_breaking ? ...
                calculate_tie_breaker(nx_coord, ny_coord, nz_coord, SourcePoints) : 0;
            
            % 添加到开放集合
            open_set = [open_set; [f_total, tie_breaker, tentative_g, nx_coord, ny_coord, nz_coord]];
        end
    end
end

total_time = toc;
fprintf('高级A*算法完成！\n');
fprintf('总用时: %.2f秒, 处理节点: %d, 效率: %.0f节点/秒\n', ...
    total_time, processed_count, processed_count/total_time);

end

function stats = calculate_speed_statistics(SpeedImage)
%CALCULATE_SPEED_STATISTICS 计算速度场统计信息

stats.mean = mean(SpeedImage(:));
stats.min = min(SpeedImage(:));
stats.max = max(SpeedImage(:));
stats.std = std(SpeedImage(:));
stats.median = median(SpeedImage(:));

end

function [neighbors, distances] = get_advanced_neighbors(connectivity)
%GET_ADVANCED_NEIGHBORS 获取高级邻居配置

switch connectivity
    case 6
        neighbors = [-1,0,0; 1,0,0; 0,-1,0; 0,1,0; 0,0,-1; 0,0,1];
        distances = ones(6, 1);
        
    case 18
        neighbors = [
            -1,0,0; 1,0,0; 0,-1,0; 0,1,0; 0,0,-1; 0,0,1;  % 面
            -1,-1,0; -1,1,0; 1,-1,0; 1,1,0;  % 边 (xy平面)
            -1,0,-1; -1,0,1; 1,0,-1; 1,0,1;  % 边 (xz平面)
            0,-1,-1; 0,-1,1; 0,1,-1; 0,1,1   % 边 (yz平面)
        ];
        distances = [ones(6,1); sqrt(2)*ones(12,1)];
        
    case 26
        neighbors = [
            -1,0,0; 1,0,0; 0,-1,0; 0,1,0; 0,0,-1; 0,0,1;  % 面
            -1,-1,0; -1,1,0; 1,-1,0; 1,1,0;  % 边
            -1,0,-1; -1,0,1; 1,0,-1; 1,0,1;
            0,-1,-1; 0,-1,1; 0,1,-1; 0,1,1;
            -1,-1,-1; -1,-1,1; -1,1,-1; -1,1,1;  % 角
            1,-1,-1; 1,-1,1; 1,1,-1; 1,1,1
        ];
        distances = [ones(6,1); sqrt(2)*ones(12,1); sqrt(3)*ones(8,1)];
        
    otherwise
        error('连接性必须是 6, 18, 或 26');
end

end

function travel_time = calculate_adaptive_travel_time(SpeedImage, x1, y1, z1, x2, y2, z2, distance)
%CALCULATE_ADAPTIVE_TRAVEL_TIME 自适应传播时间计算

v1 = SpeedImage(x1, y1, z1);
v2 = SpeedImage(x2, y2, z2);

% 根据速度差异选择平均方法
speed_ratio = max(v1, v2) / min(v1, v2);

if speed_ratio < 1.5
    % 速度相近，使用算术平均
    v_avg = (v1 + v2) / 2;
else
    % 速度差异大，使用调和平均
    v_avg = 2 / (1/max(v1, 1e-8) + 1/max(v2, 1e-8));
end

travel_time = distance / max(v_avg, 1e-8);

end

function h_cost = calculate_advanced_heuristic(x, y, z, SourcePoints, speed_stats, options)
%CALCULATE_ADVANCED_HEURISTIC 计算高级启发式代价

num_sources = size(SourcePoints, 2);
min_cost = inf;

for s = 1:num_sources
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    dx = abs(x - sx);
    dy = abs(y - sy);
    dz = abs(z - sz);
    
    switch options.heuristic_type
        case 'euclidean'
            distance = sqrt(dx^2 + dy^2 + dz^2);
            
        case 'manhattan'
            distance = dx + dy + dz;
            
        case 'octile'
            % 3D octile distance
            sorted_d = sort([dx, dy, dz], 'descend');
            distance = sorted_d(1) + (sqrt(2)-1)*sorted_d(2) + (sqrt(3)-sqrt(2))*sorted_d(3);
            
        case 'adaptive'
            % 自适应启发式，根据局部速度变化调整
            euclidean_dist = sqrt(dx^2 + dy^2 + dz^2);
            speed_factor = speed_stats.max / speed_stats.mean;
            distance = euclidean_dist * speed_factor;
            
        otherwise
            distance = sqrt(dx^2 + dy^2 + dz^2);
    end
    
    % 使用适当的速度估计
    if strcmp(options.heuristic_type, 'adaptive')
        speed_estimate = speed_stats.max;  % 乐观估计
    else
        speed_estimate = speed_stats.mean;  % 平均估计
    end
    
    cost = options.heuristic_weight * distance / speed_estimate;
    
    if cost < min_cost
        min_cost = cost;
    end
end

h_cost = min_cost;

end

function tie_breaker = calculate_tie_breaker(x, y, z, SourcePoints)
%CALCULATE_TIE_BREAKER 计算打破平局的值

% 使用到最近源点的向量叉积作为打破平局的依据
num_sources = size(SourcePoints, 2);
min_cross = inf;

for s = 1:num_sources
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    % 计算向量叉积的模长
    dx = x - sx;
    dy = y - sy;
    dz = z - sz;
    
    cross_product = abs(dx) + abs(dy) + abs(dz);  % 简化的叉积
    
    if cross_product < min_cross
        min_cross = cross_product;
    end
end

tie_breaker = min_cross * 0.001;  % 小的扰动值

end
