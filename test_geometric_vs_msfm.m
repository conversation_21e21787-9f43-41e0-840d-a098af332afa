%% 几何分析法与MSFM3D方法比较测试
% 这个脚本比较几何分析法和MSFM3D在不同场景下的性能

clear; clc;

%% 测试1: 均匀速度场
fprintf('=== 测试1: 均匀速度场 ===\n');

% 设置参数
SourcePoint = [50; 50; 50];  % 源点位置
dims = [100, 100, 100];     % 网格尺寸
uniform_speed = 1500;       % 均匀速度 (m/s)

% 创建均匀速度场
SpeedImage_uniform = ones(dims) * uniform_speed;

% 创建坐标网格
[X, Y, Z] = ndgrid(1:dims(1), 1:dims(2), 1:dims(3));

% 理论解 (欧几里得距离/速度)
T_theory = sqrt((X-SourcePoint(1)).^2 + (Y-SourcePoint(2)).^2 + (Z-SourcePoint(3)).^2) / uniform_speed;

% 几何分析法 (需要编译geometric_analysis_3d.c)
fprintf('运行几何分析法...\n');
tic;
try
    T_geometric = geometric_analysis_3d(SpeedImage_uniform, SourcePoint, 0); % 0 = 直接几何法
    time_geometric = toc;
    fprintf('几何分析法用时: %.4f 秒\n', time_geometric);
catch ME
    fprintf('几何分析法出错: %s\n', ME.message);
    fprintf('请先编译geometric_analysis_3d.c文件\n');
    T_geometric = [];
    time_geometric = NaN;
end

% MSFM3D方法
fprintf('运行MSFM3D方法...\n');
tic;
try
    T_msfm = msfm3d(SpeedImage_uniform, SourcePoint, true, true);
    time_msfm = toc;
    fprintf('MSFM3D用时: %.4f 秒\n', time_msfm);
catch ME
    fprintf('MSFM3D出错: %s\n', ME.message);
    T_msfm = [];
    time_msfm = NaN;
end

% 计算误差
if ~isempty(T_geometric)
    error_geometric = calculate_errors(T_theory, T_geometric);
    fprintf('\n几何分析法误差:\n');
    fprintf('L1: %.6f, L2: %.6f, L∞: %.6f\n', error_geometric.L1, error_geometric.L2, error_geometric.Linf);
end

if ~isempty(T_msfm)
    error_msfm = calculate_errors(T_theory, T_msfm);
    fprintf('\nMSFM3D误差:\n');
    fprintf('L1: %.6f, L2: %.6f, L∞: %.6f\n', error_msfm.L1, error_msfm.L2, error_msfm.Linf);
end

%% 测试2: 分层速度场
fprintf('\n=== 测试2: 分层速度场 ===\n');

% 创建分层速度场 (模拟地质分层)
SpeedImage_layered = ones(dims) * 1500;  % 基础速度
SpeedImage_layered(:, :, 1:33) = 2000;   % 上层: 2000 m/s
SpeedImage_layered(:, :, 34:66) = 1500;  % 中层: 1500 m/s  
SpeedImage_layered(:, :, 67:100) = 3000; % 下层: 3000 m/s

% 几何分析法 (射线追踪)
fprintf('运行几何分析法 (射线追踪)...\n');
tic;
try
    T_geometric_layered = geometric_analysis_3d(SpeedImage_layered, SourcePoint, 1); % 1 = 射线追踪
    time_geometric_layered = toc;
    fprintf('几何分析法 (射线追踪) 用时: %.4f 秒\n', time_geometric_layered);
catch ME
    fprintf('几何分析法出错: %s\n', ME.message);
    T_geometric_layered = [];
    time_geometric_layered = NaN;
end

% MSFM3D方法
fprintf('运行MSFM3D方法...\n');
tic;
try
    T_msfm_layered = msfm3d(SpeedImage_layered, SourcePoint, true, true);
    time_msfm_layered = toc;
    fprintf('MSFM3D用时: %.4f 秒\n', time_msfm_layered);
catch ME
    fprintf('MSFM3D出错: %s\n', ME.message);
    T_msfm_layered = [];
    time_msfm_layered = NaN;
end

% 比较结果 (在分层介质中没有解析解，所以比较两种方法的差异)
if ~isempty(T_geometric_layered) && ~isempty(T_msfm_layered)
    diff_layered = calculate_errors(T_msfm_layered, T_geometric_layered);
    fprintf('\n分层介质中两种方法的差异:\n');
    fprintf('L1: %.6f, L2: %.6f, L∞: %.6f\n', diff_layered.L1, diff_layered.L2, diff_layered.Linf);
end

%% 测试3: 复杂速度场 (包含异常体)
fprintf('\n=== 测试3: 包含异常体的速度场 ===\n');

% 创建包含异常体的速度场
SpeedImage_complex = ones(dims) * 1500;  % 背景速度

% 添加高速异常体 (球形)
center = [50, 50, 50];
radius = 15;
for i = 1:dims(1)
    for j = 1:dims(2)
        for k = 1:dims(3)
            dist = sqrt((i-center(1))^2 + (j-center(2))^2 + (k-center(3))^2);
            if dist <= radius
                SpeedImage_complex(i,j,k) = 3000;  % 高速异常体
            end
        end
    end
end

% 添加低速异常体 (立方体)
SpeedImage_complex(20:30, 70:80, 20:30) = 800;  % 低速异常体

% 几何分析法 (射线追踪)
fprintf('运行几何分析法 (射线追踪)...\n');
tic;
try
    T_geometric_complex = geometric_analysis_3d(SpeedImage_complex, SourcePoint, 1);
    time_geometric_complex = toc;
    fprintf('几何分析法用时: %.4f 秒\n', time_geometric_complex);
catch ME
    fprintf('几何分析法出错: %s\n', ME.message);
    T_geometric_complex = [];
    time_geometric_complex = NaN;
end

% MSFM3D方法
fprintf('运行MSFM3D方法...\n');
tic;
try
    T_msfm_complex = msfm3d(SpeedImage_complex, SourcePoint, true, true);
    time_msfm_complex = toc;
    fprintf('MSFM3D用时: %.4f 秒\n', time_msfm_complex);
catch ME
    fprintf('MSFM3D出错: %s\n', ME.message);
    T_msfm_complex = [];
    time_msfm_complex = NaN;
end

% 比较结果
if ~isempty(T_geometric_complex) && ~isempty(T_msfm_complex)
    diff_complex = calculate_errors(T_msfm_complex, T_geometric_complex);
    fprintf('\n复杂介质中两种方法的差异:\n');
    fprintf('L1: %.6f, L2: %.6f, L∞: %.6f\n', diff_complex.L1, diff_complex.L2, diff_complex.Linf);
end

%% 可视化结果 (如果有数据的话)
if exist('T_theory', 'var') && exist('T_geometric', 'var') && exist('T_msfm', 'var') && ...
   ~isempty(T_geometric) && ~isempty(T_msfm)
    
    figure('Position', [100, 100, 1200, 400]);
    
    % 选择中间切片进行显示
    slice_idx = round(dims(3)/2);
    
    subplot(1,3,1);
    imagesc(squeeze(T_theory(:,:,slice_idx)));
    colorbar; title('理论解'); axis equal tight;
    
    subplot(1,3,2);
    imagesc(squeeze(T_geometric(:,:,slice_idx)));
    colorbar; title('几何分析法'); axis equal tight;
    
    subplot(1,3,3);
    imagesc(squeeze(T_msfm(:,:,slice_idx)));
    colorbar; title('MSFM3D'); axis equal tight;
    
    sgtitle('均匀速度场中的传播时间比较 (Z切片)');
end

%% 总结
fprintf('\n=== 方法比较总结 ===\n');
fprintf('1. 均匀速度场: 几何分析法应该最准确且最快\n');
fprintf('2. 分层速度场: 两种方法各有优势\n');
fprintf('3. 复杂速度场: MSFM3D通常更准确，几何分析法更快\n');

%% 辅助函数
function errors = calculate_errors(reference, computed)
    % 计算L1, L2, L∞误差
    diff = reference(:) - computed(:);
    errors.L1 = mean(abs(diff));
    errors.L2 = sqrt(mean(diff.^2));
    errors.Linf = max(abs(diff));
end
