%% 编译Dial算法MEX文件
% 这个脚本用于编译dial_3d.c文件并进行全面测试

fprintf('正在编译Dial算法MEX文件...\n');

try
    % 编译MEX文件
    mex dial_3d.c
    fprintf('✓ 编译成功！\n');
    
    % 基础功能测试
    fprintf('\n=== 基础功能测试 ===\n');
    test_speed = ones([30, 30, 30]) * 1500;
    test_source = [15; 15; 15];
    
    % 测试不同连接性和量化因子
    connectivities = [6, 18, 26];
    scale_factors = [100, 1000, 10000];
    
    fprintf('测试不同配置...\n');
    for c = 1:length(connectivities)
        conn = connectivities(c);
        for s = 1:length(scale_factors)
            sf = scale_factors(s);
            
            fprintf('连接性=%d, 量化因子=%.0f: ', conn, sf);
            tic;
            try
                result = dial_3d(test_speed, test_source, conn, sf);
                elapsed = toc;
                
                if all(size(result) == [30, 30, 30])
                    fprintf('✓ 成功 (%.3f秒)\n', elapsed);
                else
                    fprintf('✗ 尺寸错误\n');
                end
            catch ME
                fprintf('✗ 失败: %s\n', ME.message);
            end
        end
    end
    
    % 精度验证测试
    fprintf('\n=== 精度验证测试 ===\n');
    [X, Y, Z] = ndgrid(1:30, 1:30, 1:30);
    theory = sqrt((X-15).^2 + (Y-15).^2 + (Z-15).^2) / 1500;
    
    % 测试不同量化因子对精度的影响
    fprintf('量化因子对精度的影响:\n');
    for s = 1:length(scale_factors)
        sf = scale_factors(s);
        result = dial_3d(test_speed, test_source, 6, sf);
        error = mean(abs(theory(:) - result(:)));
        fprintf('  量化因子 %.0f: 平均误差 %.6f\n', sf, error);
    end
    
    % 与MATLAB版本比较
    fprintf('\n=== 与MATLAB版本比较 ===\n');
    try
        result_c = dial_3d(test_speed, test_source, 6, 1000);
        result_matlab = dial_3d_matlab(test_speed, test_source, 6, 1000);
        
        diff_versions = mean(abs(result_c(:) - result_matlab(:)));
        fprintf('C版本与MATLAB版本差异: %.6f\n', diff_versions);
        
        if diff_versions < 1e-6
            fprintf('✓ C版本与MATLAB版本结果基本一致\n');
        else
            fprintf('⚠ C版本与MATLAB版本存在差异\n');
        end
    catch
        fprintf('MATLAB版本不可用，跳过比较\n');
    end
    
    % 性能基准测试
    fprintf('\n=== 性能基准测试 ===\n');
    sizes = [20, 40, 60, 80];
    
    for s = 1:length(sizes)
        sz = sizes(s);
        test_vol = ones([sz, sz, sz]) * 1500;
        test_src = [round(sz/2); round(sz/2); round(sz/2)];
        
        fprintf('\n网格尺寸: %dx%dx%d (%d个体素)\n', sz, sz, sz, sz^3);
        
        % 测试不同连接性的性能
        for c = 1:length(connectivities)
            conn = connectivities(c);
            fprintf('  连接性 %d: ', conn);
            
            tic;
            try
                result = dial_3d(test_vol, test_src, conn, 1000);
                elapsed = toc;
                rate = sz^3 / elapsed;
                fprintf('%.3f秒 (%.0f体素/秒)\n', elapsed, rate);
            catch ME
                fprintf('失败 (%s)\n', ME.message);
            end
        end
    end
    
    % 量化因子性能影响测试
    fprintf('\n=== 量化因子性能影响测试 ===\n');
    test_vol = ones([50, 50, 50]) * 1500;
    test_src = [25; 25; 25];
    
    large_scale_factors = [100, 500, 1000, 5000, 10000];
    fprintf('测试不同量化因子对性能的影响:\n');
    
    for s = 1:length(large_scale_factors)
        sf = large_scale_factors(s);
        fprintf('量化因子 %.0f: ', sf);
        
        tic;
        try
            result = dial_3d(test_vol, test_src, 6, sf);
            elapsed = toc;
            
            % 计算精度
            [X, Y, Z] = ndgrid(1:50, 1:50, 1:50);
            theory = sqrt((X-25).^2 + (Y-25).^2 + (Z-25).^2) / 1500;
            error = mean(abs(theory(:) - result(:)));
            
            fprintf('%.3f秒, 误差=%.6f\n', elapsed, error);
        catch ME
            fprintf('失败 (%s)\n', ME.message);
        end
    end
    
    % 与其他算法比较
    fprintf('\n=== 与其他算法比较 ===\n');
    comp_vol = ones([60, 60, 60]) * 1500;
    comp_src = [30; 30; 30];
    
    % Dial算法
    fprintf('Dial算法: ');
    tic;
    result_dial = dial_3d(comp_vol, comp_src, 6, 1000);
    time_dial = toc;
    fprintf('%.3f秒\n', time_dial);
    
    % 与其他算法比较 (如果可用)
    algorithms = {'dijkstra_3d', 'astar_3d'};
    algorithm_names = {'Dijkstra', 'A*'};
    
    for a = 1:length(algorithms)
        if exist(algorithms{a}, 'file') == 3
            fprintf('%s算法: ', algorithm_names{a});
            tic;
            if strcmp(algorithms{a}, 'dijkstra_3d')
                result_other = dijkstra_3d(comp_vol, comp_src, 6);
            else
                result_other = astar_3d(comp_vol, comp_src, 6, 1.0);
            end
            time_other = toc;
            fprintf('%.3f秒\n', time_other);
            
            % 比较结果差异
            diff = mean(abs(result_dial(:) - result_other(:)));
            fprintf('  与Dial差异: %.6f\n', diff);
            
            % 速度比较
            if time_other > time_dial
                speedup = time_other / time_dial;
                fprintf('  Dial加速比: %.2fx\n', speedup);
            else
                slowdown = time_dial / time_other;
                fprintf('  Dial减速比: %.2fx\n', slowdown);
            end
        else
            fprintf('%s算法MEX文件不可用\n', algorithm_names{a});
        end
    end
    
    % 内存使用测试
    fprintf('\n=== 内存使用分析 ===\n');
    fprintf('测试不同网格尺寸的内存使用...\n');
    
    memory_sizes = [30, 50, 70, 90];
    for s = 1:length(memory_sizes)
        sz = memory_sizes(s);
        mem_vol = ones([sz, sz, sz]) * 1500;
        mem_src = [round(sz/2); round(sz/2); round(sz/2)];
        
        fprintf('网格 %dx%dx%d: ', sz, sz, sz);
        
        % 估算内存使用
        base_memory = sz^3 * 8 / 1024^2;  % MB (double precision)
        estimated_memory = base_memory * 2;  % 估算Dial算法额外内存
        
        tic;
        try
            result = dial_3d(mem_vol, mem_src, 6, 1000);
            elapsed = toc;
            fprintf('成功 (%.2f秒, 估算内存: %.1fMB)\n', elapsed, estimated_memory);
        catch ME
            fprintf('失败 (%s)\n', ME.message);
        end
    end
    
    fprintf('\n✓ Dial算法MEX文件测试完成！\n');
    
catch ME
    fprintf('✗ 编译或测试失败: %s\n', ME.message);
    fprintf('\n可能的解决方案:\n');
    fprintf('1. 确保已安装MATLAB编译器 (运行: mex -setup)\n');
    fprintf('2. 检查dial_3d.c文件是否存在\n');
    fprintf('3. 确保C编译器已正确配置\n');
    fprintf('4. 检查系统内存是否足够\n');
    fprintf('5. 确保没有整数溢出问题\n');
end

%% 使用建议和最佳实践
fprintf('\n=== Dial算法使用建议 ===\n');
fprintf('1. 量化因子选择:\n');
fprintf('   - 低精度快速计算: 100-500\n');
fprintf('   - 标准应用: 1000-5000\n');
fprintf('   - 高精度应用: 10000+\n');
fprintf('   - 权衡: 更高的量化因子 = 更高精度 + 更多内存\n\n');

fprintf('2. 连接性选择:\n');
fprintf('   - 6-连通: 最快，适合实时应用\n');
fprintf('   - 18-连通: 平衡精度和速度\n');
fprintf('   - 26-连通: 最高精度，适合离线计算\n\n');

fprintf('3. 适用场景:\n');
fprintf('   - 均匀或分层介质: 优于Dijkstra\n');
fprintf('   - 整数化权重: 理想选择\n');
fprintf('   - 大规模网格: 内存效率高\n');
fprintf('   - 实时系统: 可预测的性能\n\n');

fprintf('4. 性能优化:\n');
fprintf('   - 根据精度需求选择量化因子\n');
fprintf('   - 大网格使用较低连接性\n');
fprintf('   - 考虑使用优化版本的MATLAB实现\n');
