%% 编译Dijkstra算法MEX文件
% 这个脚本用于编译dijkstra_3d.c文件

fprintf('正在编译Dijkstra算法MEX文件...\n');

try
    % 编译MEX文件
    mex dijkstra_3d.c
    fprintf('编译成功！\n');
    
    % 测试编译结果
    fprintf('测试编译结果...\n');
    
    % 创建简单测试数据
    test_speed = ones([20, 20, 20]) * 1500;
    test_source = [10; 10; 10];
    
    % 测试Dijkstra算法 (6-连通)
    fprintf('测试6-连通...\n');
    result_6 = dijkstra_3d(test_speed, test_source, 6);
    
    % 测试Dijkstra算法 (26-连通)
    fprintf('测试26-连通...\n');
    result_26 = dijkstra_3d(test_speed, test_source, 26);
    
    if ~isempty(result_6) && ~isempty(result_26) && ...
       all(size(result_6) == [20, 20, 20]) && all(size(result_26) == [20, 20, 20])
        fprintf('✓ 测试通过！Dijkstra算法MEX文件可以正常使用。\n');
        
        % 简单精度检查
        [X, Y, Z] = ndgrid(1:20, 1:20, 1:20);
        theory = sqrt((X-10).^2 + (Y-10).^2 + (Z-10).^2) / 1500;
        
        error_6 = mean(abs(theory(:) - result_6(:)));
        error_26 = mean(abs(theory(:) - result_26(:)));
        
        fprintf('6-连通平均误差: %.6f\n', error_6);
        fprintf('26-连通平均误差: %.6f\n', error_26);
        
        if error_26 < error_6
            fprintf('✓ 26-连通精度更高，符合预期。\n');
        end
        
    else
        fprintf('✗ 测试失败！输出结果不正确。\n');
    end
    
catch ME
    fprintf('编译或测试失败: %s\n', ME.message);
    fprintf('\n可能的解决方案:\n');
    fprintf('1. 确保已安装MATLAB编译器 (运行: mex -setup)\n');
    fprintf('2. 检查dijkstra_3d.c文件是否存在\n');
    fprintf('3. 确保C编译器已正确配置\n');
    fprintf('4. 检查系统是否支持所需的C标准库\n');
end

%% 性能基准测试
if exist('dijkstra_3d', 'file') == 3  % MEX文件存在
    fprintf('\n=== 性能基准测试 ===\n');
    
    % 测试不同尺寸的性能
    sizes = [30, 50, 80];
    connectivities = [6, 18, 26];
    
    for s = 1:length(sizes)
        sz = sizes(s);
        test_vol = ones([sz, sz, sz]) * 1500;
        test_src = [round(sz/2); round(sz/2); round(sz/2)];
        
        fprintf('\n网格尺寸: %dx%dx%d\n', sz, sz, sz);
        
        for c = 1:length(connectivities)
            conn = connectivities(c);
            fprintf('连接性 %d: ', conn);
            
            tic;
            try
                result = dijkstra_3d(test_vol, test_src, conn);
                elapsed = toc;
                fprintf('%.3f秒\n', elapsed);
            catch ME
                fprintf('失败 (%s)\n', ME.message);
            end
        end
    end
end
