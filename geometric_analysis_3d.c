#include "mex.h"
#include "math.h"
#include <stdlib.h>

/*
 * Geometric Analysis Method for 3D Distance Calculation
 * This function calculates travel times using geometric analysis instead of 
 * the Fast Marching Method. For uniform velocity fields, it uses direct 
 * Euclidean distance calculation. For non-uniform fields, it uses ray tracing.
 * 
 * T = geometric_analysis_3d(F, SourcePoints, method)
 * 
 * Inputs:
 *   F: The 3D speed/velocity image
 *   SourcePoints: A list of starting points [3 x N]
 *   method: 0 = Direct geometric (uniform field), 1 = Ray tracing (non-uniform)
 * 
 * Outputs:
 *   T: Travel time image from SourcePoints to all pixels
 */

#define INF 1e10
#define EPS 1e-8
#define MAX_ITERATIONS 10000
#define STEP_SIZE 0.1

/* Helper function to calculate 3D index */
int mindex3(int x, int y, int z, int sizx, int sizy) {
    return z*sizx*sizy + y*sizx + x;
}

/* Check if coordinates are within bounds */
bool inbounds3d(int x, int y, int z, const mwSize *dims) {
    return (x >= 0 && x < dims[0] && y >= 0 && y < dims[1] && z >= 0 && z < dims[2]);
}

/* Trilinear interpolation for velocity field */
double interpolate_velocity(double *F, double x, double y, double z, const mwSize *dims) {
    int x0 = (int)floor(x), x1 = x0 + 1;
    int y0 = (int)floor(y), y1 = y0 + 1;
    int z0 = (int)floor(z), z1 = z0 + 1;
    
    /* Clamp to bounds */
    x0 = (x0 < 0) ? 0 : ((x0 >= dims[0]) ? dims[0]-1 : x0);
    x1 = (x1 < 0) ? 0 : ((x1 >= dims[0]) ? dims[0]-1 : x1);
    y0 = (y0 < 0) ? 0 : ((y0 >= dims[1]) ? dims[1]-1 : y0);
    y1 = (y1 < 0) ? 0 : ((y1 >= dims[1]) ? dims[1]-1 : y1);
    z0 = (z0 < 0) ? 0 : ((z0 >= dims[2]) ? dims[2]-1 : z0);
    z1 = (z1 < 0) ? 0 : ((z1 >= dims[2]) ? dims[2]-1 : z1);
    
    double xd = x - x0, yd = y - y0, zd = z - z0;
    
    /* Get 8 corner values */
    double c000 = F[mindex3(x0, y0, z0, dims[0], dims[1])];
    double c001 = F[mindex3(x0, y0, z1, dims[0], dims[1])];
    double c010 = F[mindex3(x0, y1, z0, dims[0], dims[1])];
    double c011 = F[mindex3(x0, y1, z1, dims[0], dims[1])];
    double c100 = F[mindex3(x1, y0, z0, dims[0], dims[1])];
    double c101 = F[mindex3(x1, y0, z1, dims[0], dims[1])];
    double c110 = F[mindex3(x1, y1, z0, dims[0], dims[1])];
    double c111 = F[mindex3(x1, y1, z1, dims[0], dims[1])];
    
    /* Trilinear interpolation */
    double c00 = c000*(1-xd) + c100*xd;
    double c01 = c001*(1-xd) + c101*xd;
    double c10 = c010*(1-xd) + c110*xd;
    double c11 = c011*(1-xd) + c111*xd;
    
    double c0 = c00*(1-yd) + c10*yd;
    double c1 = c01*(1-yd) + c11*yd;
    
    return c0*(1-zd) + c1*zd;
}

/* Direct geometric method for uniform velocity field */
double calculate_direct_distance(double sx, double sy, double sz, 
                               double tx, double ty, double tz, 
                               double velocity) {
    double dx = tx - sx;
    double dy = ty - sy;
    double dz = tz - sz;
    double distance = sqrt(dx*dx + dy*dy + dz*dz);
    return distance / fmax(velocity, EPS);
}

/* Ray tracing method for non-uniform velocity field */
double calculate_ray_tracing(double *F, const mwSize *dims,
                           double sx, double sy, double sz,
                           double tx, double ty, double tz) {
    double dx = tx - sx;
    double dy = ty - sy;
    double dz = tz - sz;
    double total_distance = sqrt(dx*dx + dy*dy + dz*dz);
    
    if (total_distance < EPS) return 0.0;
    
    /* Normalize direction vector */
    dx /= total_distance;
    dy /= total_distance;
    dz /= total_distance;
    
    double travel_time = 0.0;
    double current_x = sx, current_y = sy, current_z = sz;
    double remaining_distance = total_distance;
    
    int iterations = 0;
    while (remaining_distance > EPS && iterations < MAX_ITERATIONS) {
        /* Get velocity at current position */
        double velocity = interpolate_velocity(F, current_x, current_y, current_z, dims);
        velocity = fmax(velocity, EPS);
        
        /* Calculate step size (adaptive) */
        double step = fmin(STEP_SIZE, remaining_distance);
        
        /* Add travel time for this segment */
        travel_time += step / velocity;
        
        /* Move to next position */
        current_x += dx * step;
        current_y += dy * step;
        current_z += dz * step;
        
        remaining_distance -= step;
        iterations++;
    }
    
    return travel_time;
}

/* Check if velocity field is uniform */
bool is_uniform_field(double *F, int npixels, double *uniform_velocity) {
    double first_value = F[0];
    *uniform_velocity = first_value;
    
    for (int i = 1; i < npixels; i++) {
        if (fabs(F[i] - first_value) > EPS) {
            return false;
        }
    }
    return true;
}

/* Main MEX function */
void mexFunction(int nlhs, mxArray *plhs[], int nrhs, const mxArray *prhs[]) {
    /* Input validation */
    if (nrhs < 2 || nrhs > 3) {
        mexErrMsgTxt("2 or 3 inputs required: F, SourcePoints, [method]");
    }
    if (nlhs != 1) {
        mexErrMsgTxt("One output required");
    }
    
    /* Check input types */
    if (mxGetClassID(prhs[0]) != mxDOUBLE_CLASS) {
        mexErrMsgTxt("Speed image must be of class double");
    }
    if (mxGetClassID(prhs[1]) != mxDOUBLE_CLASS) {
        mexErrMsgTxt("SourcePoints must be of class double");
    }
    
    /* Get input data */
    double *F = (double*)mxGetPr(prhs[0]);
    double *SourcePoints = (double*)mxGetPr(prhs[1]);
    
    /* Get method (default = 0 for auto-detection) */
    int method = 0;
    if (nrhs > 2) {
        method = (int)mxGetScalar(prhs[2]);
    }
    
    /* Get dimensions */
    const mwSize *dims = mxGetDimensions(prhs[0]);
    if (mxGetNumberOfDimensions(prhs[0]) != 3) {
        mexErrMsgTxt("Speed image must be 3D");
    }
    
    const mwSize *sp_dims = mxGetDimensions(prhs[1]);
    if (sp_dims[0] != 3) {
        mexErrMsgTxt("SourcePoints must be a 3xN matrix");
    }
    
    int npixels = dims[0] * dims[1] * dims[2];
    int num_sources = sp_dims[1];
    
    /* Create output array */
    plhs[0] = mxCreateNumericArray(3, dims, mxDOUBLE_CLASS, mxREAL);
    double *T = mxGetPr(plhs[0]);
    
    /* Initialize output with infinity */
    for (int i = 0; i < npixels; i++) {
        T[i] = INF;
    }
    
    /* Auto-detect method if not specified */
    double uniform_velocity;
    bool is_uniform = is_uniform_field(F, npixels, &uniform_velocity);
    if (method == 0) {
        method = is_uniform ? 0 : 1;  /* 0 = direct, 1 = ray tracing */
    }
    
    /* Process each pixel */
    for (int z = 0; z < dims[2]; z++) {
        for (int y = 0; y < dims[1]; y++) {
            for (int x = 0; x < dims[0]; x++) {
                int idx = mindex3(x, y, z, dims[0], dims[1]);
                double min_time = INF;
                
                /* Find minimum travel time from all sources */
                for (int s = 0; s < num_sources; s++) {
                    double sx = SourcePoints[0 + s*3] - 1;  /* Convert to 0-based */
                    double sy = SourcePoints[1 + s*3] - 1;
                    double sz = SourcePoints[2 + s*3] - 1;
                    
                    double travel_time;
                    if (method == 0 && is_uniform) {
                        /* Direct geometric method */
                        travel_time = calculate_direct_distance(sx, sy, sz, 
                                                              (double)x, (double)y, (double)z, 
                                                              uniform_velocity);
                    } else {
                        /* Ray tracing method */
                        travel_time = calculate_ray_tracing(F, dims, sx, sy, sz, 
                                                          (double)x, (double)y, (double)z);
                    }
                    
                    if (travel_time < min_time) {
                        min_time = travel_time;
                    }
                }
                
                T[idx] = min_time;
            }
        }
    }
}
