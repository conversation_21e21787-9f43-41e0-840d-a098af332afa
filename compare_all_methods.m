%% 完整方法比较：MSFM3D vs 几何分析法 vs Dijkstra算法
% 复现您的原始测试，并添加Dijkstra算法比较

clear; clc;

fprintf('=== 三种方法完整比较测试 ===\n');
fprintf('1. MSFM3D (快速行进方法)\n');
fprintf('2. 几何分析法 (直接计算)\n');
fprintf('3. Dijkstra算法 (图论最短路径)\n\n');

%% 测试参数设置
SourcePoint = [1; 1; 1];
SpeedImage = ones([200 200 200]) * 1500;

% 创建坐标网格和理论解
[X, Y, Z] = ndgrid(1:200, 1:200, 1:200);
T_theory = (sqrt((X-SourcePoint(1)).^2 + (Y-SourcePoint(2)).^2 + (Z-SourcePoint(3)).^2)) ./ SpeedImage;

fprintf('测试配置:\n');
fprintf('- 网格尺寸: 200x200x200\n');
fprintf('- 源点位置: (%d, %d, %d)\n', SourcePoint(1), SourcePoint(2), SourcePoint(3));
fprintf('- 均匀速度: %.0f m/s\n', SpeedImage(1));
fprintf('- 总体素数: %d\n\n', numel(SpeedImage));

%% 方法1: 几何分析法
fprintf('=== 方法1: 几何分析法 ===\n');
tic;
try
    T_geometric = geometric_analysis_simple(SpeedImage, SourcePoint);
    time_geometric = toc;
    fprintf('✓ 成功完成，用时: %.4f 秒\n', time_geometric);
    error_geometric = calculate_errors(T_theory, T_geometric);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_geometric.L1, error_geometric.L2, error_geometric.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_geometric = [];
    time_geometric = NaN;
    error_geometric = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法2: Dijkstra算法 (6-连通)
fprintf('\n=== 方法2: Dijkstra算法 (6-连通) ===\n');
tic;
try
    T_dijkstra_6 = dijkstra_3d_matlab(SpeedImage, SourcePoint, 6);
    time_dijkstra_6 = toc;
    fprintf('✓ 成功完成，用时: %.4f 秒\n', time_dijkstra_6);
    error_dijkstra_6 = calculate_errors(T_theory, T_dijkstra_6);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dijkstra_6.L1, error_dijkstra_6.L2, error_dijkstra_6.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dijkstra_6 = [];
    time_dijkstra_6 = NaN;
    error_dijkstra_6 = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法3: Dijkstra算法 (26-连通) - 小尺寸测试
fprintf('\n=== 方法3: Dijkstra算法 (26-连通) - 小尺寸测试 ===\n');
% 由于26-连通计算量大，使用较小网格测试
small_size = [50 50 50];
SpeedImage_small = ones(small_size) * 1500;
SourcePoint_small = [1; 1; 1];

[X_s, Y_s, Z_s] = ndgrid(1:small_size(1), 1:small_size(2), 1:small_size(3));
T_theory_small = (sqrt((X_s-SourcePoint_small(1)).^2 + (Y_s-SourcePoint_small(2)).^2 + (Z_s-SourcePoint_small(3)).^2)) ./ SpeedImage_small;

tic;
try
    T_dijkstra_26_small = dijkstra_3d_matlab(SpeedImage_small, SourcePoint_small, 26);
    time_dijkstra_26_small = toc;
    fprintf('✓ 成功完成 (50x50x50)，用时: %.4f 秒\n', time_dijkstra_26_small);
    error_dijkstra_26_small = calculate_errors(T_theory_small, T_dijkstra_26_small);
    fprintf('  精度 - L1: %.2e, L2: %.2e, L∞: %.2e\n', ...
        error_dijkstra_26_small.L1, error_dijkstra_26_small.L2, error_dijkstra_26_small.Linf);
catch ME
    fprintf('✗ 失败: %s\n', ME.message);
    T_dijkstra_26_small = [];
    time_dijkstra_26_small = NaN;
    error_dijkstra_26_small = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
end

%% 方法4-7: MSFM3D的四种配置
fprintf('\n=== 方法4-7: MSFM3D算法 ===\n');

methods_msfm = {'FMM1', 'MSFM1', 'FMM2', 'MSFM2'};
params_msfm = {[false, false], [false, true], [true, false], [true, true]};
results_msfm = cell(1, 4);
times_msfm = zeros(1, 4);
errors_msfm = cell(1, 4);

for i = 1:4
    fprintf('方法%d - %s: ', i+3, methods_msfm{i});
    tic;
    try
        results_msfm{i} = msfm3d(SpeedImage, SourcePoint, params_msfm{i}(1), params_msfm{i}(2));
        times_msfm(i) = toc;
        fprintf('✓ 用时: %.4f 秒\n', times_msfm(i));
        errors_msfm{i} = calculate_errors(T_theory, results_msfm{i});
    catch ME
        fprintf('✗ 失败: %s\n', ME.message);
        results_msfm{i} = [];
        times_msfm(i) = NaN;
        errors_msfm{i} = struct('L1', NaN, 'L2', NaN, 'Linf', NaN);
    end
end

%% 结果汇总表
fprintf('\n=== 完整结果汇总 ===\n');
fprintf('%-15s %12s %12s %12s %12s\n', '方法', 'L1误差', 'L2误差', 'L∞误差', '时间(秒)');
fprintf('%s\n', repmat('-', 1, 70));

% 几何分析法
if ~isempty(T_geometric)
    fprintf('%-15s %12.2e %12.2e %12.2e %12.4f\n', '几何分析法', ...
        error_geometric.L1, error_geometric.L2, error_geometric.Linf, time_geometric);
end

% Dijkstra 6-连通
if ~isempty(T_dijkstra_6)
    fprintf('%-15s %12.2e %12.2e %12.2e %12.4f\n', 'Dijkstra-6', ...
        error_dijkstra_6.L1, error_dijkstra_6.L2, error_dijkstra_6.Linf, time_dijkstra_6);
end

% MSFM方法
for i = 1:4
    if ~isempty(results_msfm{i})
        fprintf('%-15s %12.2e %12.2e %12.2e %12.4f\n', methods_msfm{i}, ...
            errors_msfm{i}.L1, errors_msfm{i}.L2, errors_msfm{i}.Linf, times_msfm(i));
    end
end

%% 可视化比较
if ~isempty(T_geometric) && ~isempty(T_dijkstra_6) && ~isempty(results_msfm{4})
    slice_idx = 100;
    
    figure('Position', [100, 100, 1200, 800]);
    
    % 理论解
    subplot(2, 3, 1);
    imagesc(squeeze(T_theory(:, :, slice_idx)));
    colorbar; title('理论解'); axis equal tight;
    
    % 几何分析法
    subplot(2, 3, 2);
    imagesc(squeeze(T_geometric(:, :, slice_idx)));
    colorbar; title('几何分析法'); axis equal tight;
    
    % Dijkstra 6-连通
    subplot(2, 3, 3);
    imagesc(squeeze(T_dijkstra_6(:, :, slice_idx)));
    colorbar; title('Dijkstra-6'); axis equal tight;
    
    % MSFM2
    subplot(2, 3, 4);
    imagesc(squeeze(results_msfm{4}(:, :, slice_idx)));
    colorbar; title('MSFM2'); axis equal tight;
    
    % 误差比较：几何分析法 vs Dijkstra
    subplot(2, 3, 5);
    error_diff = T_geometric - T_dijkstra_6;
    imagesc(squeeze(error_diff(:, :, slice_idx)));
    colorbar; title('几何法 - Dijkstra差异'); axis equal tight;
    
    % 误差比较：Dijkstra vs MSFM2
    subplot(2, 3, 6);
    error_diff2 = T_dijkstra_6 - results_msfm{4};
    imagesc(squeeze(error_diff2(:, :, slice_idx)));
    colorbar; title('Dijkstra - MSFM2差异'); axis equal tight;
    
    sgtitle(sprintf('方法比较 (Z=%d切片)', slice_idx));
end

%% 算法特性分析
fprintf('\n=== 算法特性分析 ===\n');
fprintf('1. 几何分析法:\n');
fprintf('   - 优点: 在均匀介质中理论精确，计算最快\n');
fprintf('   - 缺点: 仅适用于简单介质结构\n');

fprintf('\n2. Dijkstra算法:\n');
fprintf('   - 优点: 图论基础扎实，保证找到最短路径\n');
fprintf('   - 缺点: 计算复杂度高，内存需求大\n');
fprintf('   - 连接性影响: 26-连通比6-连通更精确但更慢\n');

fprintf('\n3. MSFM算法:\n');
fprintf('   - 优点: 专为波传播设计，精度和速度平衡好\n');
fprintf('   - 缺点: 实现复杂，需要数值求解\n');

%% 性能建议
fprintf('\n=== 应用建议 ===\n');
fprintf('• 均匀介质: 推荐几何分析法 (最快最准)\n');
fprintf('• 简单分层介质: 推荐MSFM2 (精度和速度平衡)\n');
fprintf('• 复杂介质: 推荐MSFM2或Dijkstra-26 (高精度)\n');
fprintf('• 大规模计算: 避免使用Dijkstra-26 (内存和时间消耗大)\n');

%% 辅助函数
function errors = calculate_errors(reference, computed)
    diff = reference(:) - computed(:);
    errors.L1 = mean(abs(diff));
    errors.L2 = sqrt(mean(diff.^2));
    errors.Linf = max(abs(diff));
end
