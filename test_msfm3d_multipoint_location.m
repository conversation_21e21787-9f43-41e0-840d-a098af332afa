% MSFM3D 三维地震源多点定位演示代码
% 基于多模板快速行进方法的3D多点定位算法测试

clear; clc; close all;

fprintf('=== MSFM3D 三维地震源多点定位演示 ===\n\n');

%% 1. 建立3D速度模型
fprintf('1. 构建3D速度模型...\n');

% 模型尺寸
nx = 100; ny = 100; nz = 50;
SpeedImage3D = zeros(nx, ny, nz);

% 创建分层速度模型
for k = 1:nz
    depth_ratio = k / nz;
    
    % 基础分层速度结构
    if depth_ratio <= 0.2
        base_velocity = 2000;  % 表层
    elseif depth_ratio <= 0.4
        base_velocity = 3500;  % 第二层
    elseif depth_ratio <= 0.6
        base_velocity = 4500;  % 第三层
    elseif depth_ratio <= 0.8
        base_velocity = 5500;  % 第四层
    else
        base_velocity = 6500;  % 底层
    end
    
    % 添加横向速度变化
    for i = 1:nx
        for j = 1:ny
            % 添加横向梯度和随机扰动
            lateral_variation = 1 + 0.1 * sin(2*pi*i/nx) * cos(2*pi*j/ny);
            random_variation = 1 + 0.05 * (rand() - 0.5);
            
            SpeedImage3D(i, j, k) = base_velocity * lateral_variation * random_variation;
        end
    end
end

% 添加低速异常体（模拟断层或破碎带）
SpeedImage3D(30:40, 30:40, 15:25) = 1500;  % 异常体1
SpeedImage3D(60:70, 60:70, 20:30) = 1800;  % 异常体2
SpeedImage3D(45:55, 20:30, 10:20) = 1600;  % 异常体3

% 确保速度值在合理范围内
SpeedImage3D = max(SpeedImage3D, 1000);  % 最小速度1000 m/s
SpeedImage3D = min(SpeedImage3D, 8000);  % 最大速度8000 m/s

fprintf('   模型尺寸: %d × %d × %d\n', nx, ny, nz);
fprintf('   速度范围: %.0f - %.0f m/s\n', min(SpeedImage3D(:)), max(SpeedImage3D(:)));

%% 2. 设置台站位置（3D坐标）
fprintf('\n2. 设置台站网络...\n');

stations = [10, 10, 1;     % 台站1 - 左下角
           90, 10, 1;     % 台站2 - 右下角
           90, 90, 1;     % 台站3 - 右上角
           10, 90, 1;     % 台站4 - 左上角
           50, 50, 1;     % 台站5 - 中心
           30, 30, 1;     % 台站6
           70, 70, 1;     % 台站7
           30, 70, 1;     % 台站8
           70, 30, 1];    % 台站9

num_stations = size(stations, 1);
fprintf('   台站数量: %d\n', num_stations);

%% 3. 设置多个测试震源位置
fprintf('\n3. 设置测试震源位置...\n');

test_sources = [
    25, 25, 12;   % 测试点1 - 浅层左下
    75, 25, 12;   % 测试点2 - 浅层右下
    75, 75, 12;   % 测试点3 - 浅层右上
    25, 75, 12;   % 测试点4 - 浅层左上
    50, 50, 15;   % 测试点5 - 浅层中心
    35, 35, 25;   % 测试点6 - 中层
    65, 65, 25;   % 测试点7 - 中层
    45, 55, 35;   % 测试点8 - 深层
    55, 45, 35;   % 测试点9 - 深层
    50, 50, 30    % 测试点10 - 深层中心
];

num_tests = size(test_sources, 1);
fprintf('   测试震源数量: %d\n', num_tests);

% 显示所有测试震源
for i = 1:num_tests
    fprintf('   测试点%d: [%d, %d, %d]\n', i, test_sources(i,1), test_sources(i,2), test_sources(i,3));
end

%% 4. 计算各台站的理论走时表
fprintf('\n4. 计算各台站的走时表...\n');
travel_time_tables = cell(num_stations, 1);

total_time = 0;
for i = 1:num_stations
    tic;
    fprintf('   计算台站 %d 走时表...', i);
    
    % 使用MSFM3D计算从台站到所有网格点的走时
    travel_time_tables{i} = msfm3d(SpeedImage3D, stations(i,:)', true, true);
    
    elapsed = toc;
    total_time = total_time + elapsed;
    fprintf(' 完成 (%.2f秒)\n', elapsed);
end

fprintf('   总计算时间: %.2f秒\n', total_time);

%% 5. 多点定位测试
fprintf('\n5. 开始多点定位测试...\n');

% 存储结果
location_errors = zeros(num_tests, 1);
estimated_sources = zeros(num_tests, 3);
residuals = zeros(num_tests, 1);
computation_times = zeros(num_tests, 1);

% 搜索参数
search_step = 5;  % 搜索步长
search_range_x = 20:search_step:80;
search_range_y = 20:search_step:80;  
search_range_z = 10:search_step:40;

fprintf('   搜索范围: X[%d:%d:%d], Y[%d:%d:%d], Z[%d:%d:%d]\n', ...
    search_range_x(1), search_step, search_range_x(end), ...
    search_range_y(1), search_step, search_range_y(end), ...
    search_range_z(1), search_step, search_range_z(end));

total_search_points = length(search_range_x) * length(search_range_y) * length(search_range_z);
fprintf('   每次搜索点数: %d\n', total_search_points);

% 对每个测试震源进行定位
for test_idx = 1:num_tests
    true_source = test_sources(test_idx, :);
    fprintf('\n--- 测试震源 %d: [%d, %d, %d] ---\n', test_idx, ...
        true_source(1), true_source(2), true_source(3));
    
    % 生成观测走时数据
    observed_times = zeros(num_stations, 1);
    noise_level = 0.002;  % 2ms噪声标准差
    
    for i = 1:num_stations
        % 从走时表中提取真实震源位置的走时
        observed_times(i) = travel_time_tables{i}(true_source(1), true_source(2), true_source(3));
        % 添加观测噪声
        observed_times(i) = observed_times(i) + noise_level * randn();
    end
    
    fprintf('观测走时: ');
    for i = 1:num_stations
        fprintf('%.4f ', observed_times(i));
    end
    fprintf('秒\n');
    
    % 网格搜索定位
    tic;
    min_residual = inf;
    best_location = [0, 0, 0];
    search_count = 0;
    
    for i = 1:length(search_range_x)
        for j = 1:length(search_range_y)
            for k = 1:length(search_range_z)
                search_count = search_count + 1;
                
                x = search_range_x(i);
                y = search_range_y(j);
                z = search_range_z(k);
                
                % 计算该位置到各台站的理论走时
                theoretical_times = zeros(num_stations, 1);
                for s = 1:num_stations
                    theoretical_times(s) = travel_time_tables{s}(x, y, z);
                end
                
                % 计算走时残差（RMS）
                residual = sqrt(mean((observed_times - theoretical_times).^2));
                
                % 更新最佳位置
                if residual < min_residual
                    min_residual = residual;
                    best_location = [x, y, z];
                end
            end
        end
    end
    
    search_time = toc;
    
    % 计算定位误差
    location_error = norm(true_source - best_location);
    
    % 存储结果
    location_errors(test_idx) = location_error;
    estimated_sources(test_idx, :) = best_location;
    residuals(test_idx) = min_residual;
    computation_times(test_idx) = search_time;
    
    % 输出单个结果
    fprintf('定位结果: [%d, %d, %d]\n', best_location(1), best_location(2), best_location(3));
    fprintf('定位误差: %.2f 网格单位\n', location_error);
    fprintf('走时残差: %.6f 秒\n', min_residual);
    fprintf('计算时间: %.2f 秒\n', search_time);
end

%% 6. 统计分析
fprintf('\n=== 多点定位统计分析 ===\n');
fprintf('定位误差统计:\n');
fprintf('  平均误差: %.2f 网格单位\n', mean(location_errors));
fprintf('  误差标准差: %.2f 网格单位\n', std(location_errors));
fprintf('  最大误差: %.2f 网格单位\n', max(location_errors));
fprintf('  最小误差: %.2f 网格单位\n', min(location_errors));
fprintf('  RMS误差: %.2f 网格单位\n', sqrt(mean(location_errors.^2)));

fprintf('\n走时残差统计:\n');
fprintf('  平均残差: %.6f 秒\n', mean(residuals));
fprintf('  残差标准差: %.6f 秒\n', std(residuals));
fprintf('  最大残差: %.6f 秒\n', max(residuals));
fprintf('  最小残差: %.6f 秒\n', min(residuals));

fprintf('\n计算时间统计:\n');
fprintf('  平均时间: %.2f 秒\n', mean(computation_times));
fprintf('  总计算时间: %.2f 秒\n', sum(computation_times));
fprintf('  最长时间: %.2f 秒\n', max(computation_times));
fprintf('  最短时间: %.2f 秒\n', min(computation_times));

%% 7. 详细结果表格
fprintf('\n=== 详细定位结果 ===\n');
fprintf('测试点\t真实位置\t\t定位结果\t\t误差\t残差(ms)\t时间(s)\n');
fprintf('------\t--------\t\t--------\t\t----\t-------\t-------\n');
for i = 1:num_tests
    fprintf('%d\t[%2d,%2d,%2d]\t\t[%2d,%2d,%2d]\t\t%.2f\t%.3f\t\t%.2f\n', ...
        i, test_sources(i,1), test_sources(i,2), test_sources(i,3), ...
        estimated_sources(i,1), estimated_sources(i,2), estimated_sources(i,3), ...
        location_errors(i), residuals(i)*1000, computation_times(i));
end

%% 8. 可视化结果
fprintf('\n6. 生成可视化图形...\n');

% 图1: 多点定位结果总览
figure(1);
set(gcf, 'Position', [100, 100, 1400, 1000]);

% 子图1: 3D散点图显示所有定位结果
subplot(2, 3, 1);
% 绘制台站
scatter3(stations(:,1), stations(:,2), stations(:,3), 150, '^k', 'filled');
hold on;

% 绘制真实震源和定位结果
colors = lines(num_tests);
for i = 1:num_tests
    % 真实震源
    scatter3(test_sources(i,1), test_sources(i,2), test_sources(i,3), ...
        200, colors(i,:), '*', 'LineWidth', 2);
    % 定位结果
    scatter3(estimated_sources(i,1), estimated_sources(i,2), estimated_sources(i,3), ...
        100, colors(i,:), 'o', 'LineWidth', 2);
    % 连接线显示误差
    plot3([test_sources(i,1), estimated_sources(i,1)], ...
          [test_sources(i,2), estimated_sources(i,2)], ...
          [test_sources(i,3), estimated_sources(i,3)], ...
          '--', 'Color', colors(i,:), 'LineWidth', 1);
end

xlabel('X'); ylabel('Y'); zlabel('Z');
title('3D多点定位结果对比');
legend('台站', '真实震源', '定位结果', 'Location', 'best');
grid on; axis equal;

% 子图2: XY平面投影
subplot(2, 3, 2);
slice_z = round(nz/3);
pcolor(squeeze(SpeedImage3D(:,:,slice_z)));
shading interp; colormap(jet); colorbar;
hold on;
plot(stations(:,2), stations(:,1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
for i = 1:num_tests
    plot(test_sources(i,2), test_sources(i,1), '*', 'Color', colors(i,:), 'MarkerSize', 12);
    plot(estimated_sources(i,2), estimated_sources(i,1), 'o', 'Color', colors(i,:), 'MarkerSize', 8);
    % 添加测试点编号
    text(test_sources(i,2)+2, test_sources(i,1)+2, num2str(i), 'FontSize', 8, 'Color', colors(i,:));
end
title(sprintf('XY平面投影 (Z=%d层)', slice_z));
xlabel('Y'); ylabel('X');

% 子图3: XZ平面投影
subplot(2, 3, 3);
slice_y = round(ny/2);
pcolor(squeeze(SpeedImage3D(:,slice_y,:))');
shading interp; colormap(jet); colorbar;
hold on;
for i = 1:num_tests
    plot(test_sources(i,1), test_sources(i,3), '*', 'Color', colors(i,:), 'MarkerSize', 12);
    plot(estimated_sources(i,1), estimated_sources(i,3), 'o', 'Color', colors(i,:), 'MarkerSize', 8);
    text(test_sources(i,1)+2, test_sources(i,3)+1, num2str(i), 'FontSize', 8, 'Color', colors(i,:));
end
title(sprintf('XZ平面投影 (Y=%d切面)', slice_y));
xlabel('X'); ylabel('Z');

% 子图4: 定位误差分布
subplot(2, 3, 4);
bar(1:num_tests, location_errors, 'FaceColor', [0.3 0.6 0.9]);
title('各测试点的定位误差');
xlabel('测试点编号'); ylabel('误差 (网格单位)');
grid on;
% 添加平均线
hold on;
mean_error = mean(location_errors);
plot([0.5, num_tests+0.5], [mean_error, mean_error], 'r--', 'LineWidth', 2);
text(num_tests*0.7, mean_error*1.1, sprintf('平均: %.2f', mean_error), 'FontSize', 10);

% 子图5: 走时残差分布
subplot(2, 3, 5);
bar(1:num_tests, residuals*1000, 'FaceColor', [0.9 0.6 0.3]); % 转换为毫秒
title('各测试点的走时残差');
xlabel('测试点编号'); ylabel('残差 (毫秒)');
grid on;
% 添加平均线
hold on;
mean_residual = mean(residuals)*1000;
plot([0.5, num_tests+0.5], [mean_residual, mean_residual], 'r--', 'LineWidth', 2);
text(num_tests*0.7, mean_residual*1.1, sprintf('平均: %.2f ms', mean_residual), 'FontSize', 10);

% 子图6: 计算时间分布
subplot(2, 3, 6);
bar(1:num_tests, computation_times, 'FaceColor', [0.6 0.9 0.6]);
title('各测试点的计算时间');
xlabel('测试点编号'); ylabel('时间 (秒)');
grid on;
% 添加平均线
hold on;
mean_time = mean(computation_times);
plot([0.5, num_tests+0.5], [mean_time, mean_time], 'r--', 'LineWidth', 2);
text(num_tests*0.7, mean_time*1.1, sprintf('平均: %.2f s', mean_time), 'FontSize', 10);

% 图2: 误差分析详图
figure(2);
set(gcf, 'Position', [200, 200, 1200, 800]);

% 误差与深度的关系
subplot(2, 2, 1);
depths = test_sources(:, 3);
scatter(depths, location_errors, 100, 'filled');
xlabel('震源深度'); ylabel('定位误差 (网格单位)');
title('定位误差 vs 震源深度');
grid on;
% 添加趋势线
p = polyfit(depths, location_errors, 1);
hold on;
plot(depths, polyval(p, depths), 'r--', 'LineWidth', 2);
correlation = corrcoef(depths, location_errors);
text(0.1, 0.9, sprintf('相关系数: %.3f', correlation(1,2)), 'Units', 'normalized', 'FontSize', 10);

% 误差与台站距离的关系
subplot(2, 2, 2);
station_center = mean(stations(:, 1:2));
distances = zeros(num_tests, 1);
for i = 1:num_tests
    distances(i) = norm(test_sources(i, 1:2) - station_center);
end
scatter(distances, location_errors, 100, 'filled');
xlabel('到台站网络中心距离'); ylabel('定位误差 (网格单位)');
title('定位误差 vs 台站距离');
grid on;
% 添加趋势线
p2 = polyfit(distances, location_errors, 1);
hold on;
plot(distances, polyval(p2, distances), 'r--', 'LineWidth', 2);
correlation2 = corrcoef(distances, location_errors);
text(0.1, 0.9, sprintf('相关系数: %.3f', correlation2(1,2)), 'Units', 'normalized', 'FontSize', 10);

% X、Y、Z方向的误差分量
subplot(2, 2, 3);
error_components = abs(test_sources - estimated_sources);
bar(1:num_tests, error_components);
title('各方向误差分量');
xlabel('测试点编号'); ylabel('误差分量 (网格单位)');
legend('X方向', 'Y方向', 'Z方向', 'Location', 'best');
grid on;

% 误差统计直方图
subplot(2, 2, 4);
histogram(location_errors, 'BinWidth', 0.5, 'FaceColor', [0.6 0.9 0.6]);
title('定位误差分布直方图');
xlabel('误差 (网格单位)'); ylabel('频次');
grid on;
% 添加统计信息
mean_error = mean(location_errors);
std_error = std(location_errors);
text(0.6, 0.8, sprintf('平均: %.2f', mean_error), 'Units', 'normalized', 'FontSize', 10);
text(0.6, 0.7, sprintf('标准差: %.2f', std_error), 'Units', 'normalized', 'FontSize', 10);
text(0.6, 0.6, sprintf('RMS: %.2f', sqrt(mean(location_errors.^2))), 'Units', 'normalized', 'FontSize', 10);

% 图3: 个别测试点的详细分析（选择误差最大和最小的点）
figure(3);
set(gcf, 'Position', [300, 300, 1000, 600]);

[~, max_error_idx] = max(location_errors);
[~, min_error_idx] = min(location_errors);

% 最大误差点的走时对比
subplot(1, 2, 1);
true_source_max = test_sources(max_error_idx, :);
estimated_source_max = estimated_sources(max_error_idx, :);

% 计算各台站的理论走时
observed_times_max = zeros(num_stations, 1);
theoretical_times_max = zeros(num_stations, 1);
for i = 1:num_stations
    observed_times_max(i) = travel_time_tables{i}(true_source_max(1), true_source_max(2), true_source_max(3));
    theoretical_times_max(i) = travel_time_tables{i}(estimated_source_max(1), estimated_source_max(2), estimated_source_max(3));
end

bar(1:num_stations, [observed_times_max, theoretical_times_max]);
title(sprintf('最大误差点 %d: 误差=%.2f', max_error_idx, location_errors(max_error_idx)));
xlabel('台站编号'); ylabel('走时 (秒)');
legend('真实走时', '定位走时', 'Location', 'best');
grid on;

% 最小误差点的走时对比
subplot(1, 2, 2);
true_source_min = test_sources(min_error_idx, :);
estimated_source_min = estimated_sources(min_error_idx, :);

% 计算各台站的理论走时
observed_times_min = zeros(num_stations, 1);
theoretical_times_min = zeros(num_stations, 1);
for i = 1:num_stations
    observed_times_min(i) = travel_time_tables{i}(true_source_min(1), true_source_min(2), true_source_min(3));
    theoretical_times_min(i) = travel_time_tables{i}(estimated_source_min(1), estimated_source_min(2), estimated_source_min(3));
end

bar(1:num_stations, [observed_times_min, theoretical_times_min]);
title(sprintf('最小误差点 %d: 误差=%.2f', min_error_idx, location_errors(min_error_idx)));
xlabel('台站编号'); ylabel('走时 (秒)');
legend('真实走时', '定位走时', 'Location', 'best');
grid on;

fprintf('可视化完成！\n');
fprintf('\n=== 多点定位演示完成 ===\n');
