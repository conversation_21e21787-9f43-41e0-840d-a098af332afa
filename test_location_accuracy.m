% 测试地震定位精度的简化版本
function test_location_accuracy()
clear; clc;

fprintf('=== 地震定位精度测试 (简化版) ===\n\n');

% ===== 1. 创建简化的测试环境 =====
fprintf('1. 创建简化测试环境...\n');

% 使用较小的模型进行快速测试
model_size = 200;
SpeedImage = create_simple_velocity_model(model_size);

% 设置台站（避开边界）
stations = [40, 40;     % 台站1
           160, 40;     % 台站2  
           160, 160;    % 台站3
           40, 160];    % 台站4

% 测试震源（在台站网络内部）
test_sources = [80, 80;    % 中心位置
               100, 120;   % 偏离中心
               60, 140];   % 靠近边界

num_tests = size(test_sources, 1);
num_stations = size(stations, 1);

fprintf('模型大小: %d × %d\n', model_size, model_size);
fprintf('台站数量: %d\n', num_stations);
fprintf('测试震源数量: %d\n', num_tests);

% ===== 2. 计算走时表 =====
fprintf('\n2. 计算走时表...\n');

travel_time_tables = cell(num_stations, 1);
for i = 1:num_stations
    fprintf('  计算台站%d走时表...', i);
    tic;
    
    % 使用简化方法确保稳定性
    travel_time_tables{i} = compute_travel_times_eikonal(SpeedImage, stations(i,:)');
    
    elapsed = toc;
    fprintf(' 完成 (%.2f秒)\n', elapsed);
    
    % 验证走时表
    validate_travel_times(travel_time_tables{i}, stations(i,:), i);
end

% ===== 3. 执行定位测试 =====
fprintf('\n3. 执行定位测试...\n');

location_errors = zeros(num_tests, 1);
estimated_sources = zeros(num_tests, 2);

for test_idx = 1:num_tests
    true_source = test_sources(test_idx, :);
    fprintf('\n--- 测试震源 %d: [%d, %d] ---\n', test_idx, true_source(1), true_source(2));
    
    % 生成观测数据
    observed_times = generate_synthetic_observations(true_source, travel_time_tables, stations);
    
    % 执行定位
    estimated_source = perform_grid_search(observed_times, travel_time_tables);
    
    % 计算误差
    error = norm(true_source - estimated_source);
    
    % 存储结果
    location_errors(test_idx) = error;
    estimated_sources(test_idx, :) = estimated_source;
    
    % 输出结果
    fprintf('真实位置: [%d, %d]\n', true_source(1), true_source(2));
    fprintf('定位结果: [%d, %d]\n', estimated_source(1), estimated_source(2));
    fprintf('定位误差: %.2f 网格单位\n', error);
    
    % 评估误差合理性
    if error <= 5
        fprintf('✅ 误差很小，定位精度很高\n');
    elseif error <= 10
        fprintf('✅ 误差合理，定位精度良好\n');
    elseif error <= 15
        fprintf('⚠️  误差偏大但可接受\n');
    else
        fprintf('❌ 误差过大，需要检查算法\n');
    end
end

% ===== 4. 结果分析 =====
fprintf('\n=== 结果分析 ===\n');
mean_error = mean(location_errors);
std_error = std(location_errors);
max_error = max(location_errors);
min_error = min(location_errors);

fprintf('平均误差: %.2f 网格单位\n', mean_error);
fprintf('误差标准差: %.2f 网格单位\n', std_error);
fprintf('误差范围: %.2f - %.2f 网格单位\n', min_error, max_error);

% 评估整体性能
if mean_error <= 8 && max_error <= 15
    fprintf('✅ 定位算法性能优秀\n');
elseif mean_error <= 12 && max_error <= 20
    fprintf('✅ 定位算法性能良好\n');
else
    fprintf('⚠️  定位算法需要改进\n');
end

% ===== 5. 可视化结果 =====
fprintf('\n5. 生成可视化结果...\n');
visualize_test_results(SpeedImage, stations, test_sources, estimated_sources, location_errors);

fprintf('\n=== 测试完成 ===\n');
end

function SpeedImage = create_simple_velocity_model(size)
% 创建简单的分层速度模型
SpeedImage = zeros(size, size);

% 三层结构
layer1_bottom = round(size * 0.3);
layer2_bottom = round(size * 0.7);

SpeedImage(1:layer1_bottom, :) = 2000;           % 第一层: 2000 m/s
SpeedImage(layer1_bottom+1:layer2_bottom, :) = 3500;  % 第二层: 3500 m/s
SpeedImage(layer2_bottom+1:end, :) = 5000;       % 第三层: 5000 m/s

% 添加一个小的低速异常
anomaly_x = round(size*0.6):round(size*0.8);
anomaly_y = round(size*0.4):round(size*0.6);
SpeedImage(anomaly_x, anomaly_y) = 1500;  % 低速异常

fprintf('创建了简单的三层速度模型\n');
end

function T = compute_travel_times_eikonal(SpeedImage, source_pos)
% 使用简化的Eikonal方程求解器
[rows, cols] = size(SpeedImage);
T = inf(rows, cols);

source_x = round(source_pos(1));
source_y = round(source_pos(2));

% 确保震源在有效范围内
source_x = max(1, min(rows, source_x));
source_y = max(1, min(cols, source_y));

% 初始化
T(source_x, source_y) = 0;

% 使用简单的波前扩展方法
max_iterations = rows * cols;
tolerance = 1e-6;

for iter = 1:max_iterations
    T_old = T;
    updated = false;
    
    for i = 2:rows-1
        for j = 2:cols-1
            if T(i, j) == inf
                continue;
            end
            
            % 更新邻居点
            neighbors = [[i-1,j]; [i+1,j]; [i,j-1]; [i,j+1]];
            
            for n = 1:size(neighbors, 1)
                ni = neighbors(n, 1);
                nj = neighbors(n, 2);
                
                if ni >= 1 && ni <= rows && nj >= 1 && nj <= cols
                    velocity = SpeedImage(ni, nj);
                    new_time = T(i, j) + 1.0 / velocity;  % 简化距离为1
                    
                    if new_time < T(ni, nj)
                        T(ni, nj) = new_time;
                        updated = true;
                    end
                end
            end
        end
    end
    
    if ~updated || max(abs(T(:) - T_old(:))) < tolerance
        break;
    end
end

% 处理仍为无穷大的点
infinite_mask = isinf(T);
if any(infinite_mask(:))
    % 使用距离和平均速度估算
    [X, Y] = meshgrid(1:cols, 1:rows);
    distances = sqrt((X - source_y).^2 + (Y - source_x).^2);
    avg_velocity = mean(SpeedImage(:));
    T(infinite_mask) = distances(infinite_mask) / avg_velocity;
end

end

function validate_travel_times(T, station_pos, station_id)
% 验证走时表的基本合理性
station_x = round(station_pos(1));
station_y = round(station_pos(2));

% 检查台站位置走时
if station_x >= 1 && station_x <= size(T, 1) && ...
   station_y >= 1 && station_y <= size(T, 2)
    station_time = T(station_x, station_y);
    if abs(station_time) > 0.001
        fprintf('    警告: 台站%d位置走时异常 (%.4f)\n', station_id, station_time);
    end
end

% 检查有效走时数量
valid_times = T(isfinite(T) & T >= 0);
total_points = numel(T);
valid_ratio = length(valid_times) / total_points;

if valid_ratio < 0.9
    fprintf('    警告: 台站%d有效走时比例较低 (%.1f%%)\n', station_id, valid_ratio*100);
end
end

function observed_times = generate_synthetic_observations(true_source, travel_time_tables, stations)
% 生成合成观测数据
num_stations = length(travel_time_tables);
observed_times = zeros(num_stations, 1);

for i = 1:num_stations
    % 提取理论走时
    x = true_source(1);
    y = true_source(2);
    
    if x >= 1 && x <= size(travel_time_tables{i}, 1) && ...
       y >= 1 && y <= size(travel_time_tables{i}, 2)
        theoretical_time = travel_time_tables{i}(x, y);
    else
        % 边界外使用距离估算
        dist = norm(true_source - stations(i, :));
        theoretical_time = dist / 3000;  % 假设平均速度
    end
    
    % 添加现实的观测噪声
    noise_std = 0.003;  % 3ms标准差
    noise = noise_std * randn();
    
    observed_times(i) = theoretical_time + noise;
    observed_times(i) = max(observed_times(i), 1e-6);  % 确保为正
end
end

function best_location = perform_grid_search(observed_times, travel_time_tables)
% 执行网格搜索定位
search_step = 4;  % 4个网格单位的搜索步长
search_range_x = 20:search_step:180;
search_range_y = 20:search_step:180;

min_residual = inf;
best_location = [100, 100];  % 默认中心位置

for i = 1:length(search_range_x)
    for j = 1:length(search_range_y)
        x = search_range_x(i);
        y = search_range_y(j);
        
        % 计算理论走时
        theoretical_times = zeros(length(travel_time_tables), 1);
        valid = true;
        
        for k = 1:length(travel_time_tables)
            if x >= 1 && x <= size(travel_time_tables{k}, 1) && ...
               y >= 1 && y <= size(travel_time_tables{k}, 2)
                theoretical_times(k) = travel_time_tables{k}(x, y);
                
                if ~isfinite(theoretical_times(k)) || theoretical_times(k) <= 0
                    valid = false;
                    break;
                end
            else
                valid = false;
                break;
            end
        end
        
        if valid
            % 计算RMS残差
            residual = sqrt(mean((observed_times - theoretical_times).^2));
            
            if residual < min_residual
                min_residual = residual;
                best_location = [x, y];
            end
        end
    end
end
end

function visualize_test_results(SpeedImage, stations, true_sources, estimated_sources, errors)
% 可视化测试结果
figure('Name', '地震定位精度测试结果', 'Position', [100, 100, 1000, 600]);

% 主图
subplot(1, 2, 1);
pcolor(SpeedImage); shading interp; colormap(jet); colorbar;
hold on;

% 绘制台站
plot(stations(:,2), stations(:,1), '^k', 'MarkerSize', 12, 'MarkerFaceColor', 'k');

% 绘制测试结果
colors = ['r', 'g', 'b'];
for i = 1:size(true_sources, 1)
    % 真实震源
    plot(true_sources(i,2), true_sources(i,1), '*', 'Color', colors(i), ...
        'MarkerSize', 15, 'LineWidth', 3);
    % 定位结果
    plot(estimated_sources(i,2), estimated_sources(i,1), 'o', 'Color', colors(i), ...
        'MarkerSize', 10, 'LineWidth', 2);
    % 误差线
    plot([true_sources(i,2), estimated_sources(i,2)], ...
        [true_sources(i,1), estimated_sources(i,1)], '--', 'Color', colors(i), 'LineWidth', 2);
    
    % 标注误差
    mid_x = (true_sources(i,2) + estimated_sources(i,2)) / 2;
    mid_y = (true_sources(i,1) + estimated_sources(i,1)) / 2;
    text(mid_x, mid_y, sprintf('%.1f', errors(i)), 'FontSize', 12, 'FontWeight', 'bold', ...
         'BackgroundColor', 'white', 'EdgeColor', 'black');
end

title('定位结果对比');
xlabel('Y坐标'); ylabel('X坐标');
legend('速度模型', '台站', '真实震源', '定位结果', 'Location', 'best');

% 误差分析
subplot(1, 2, 2);
bar(1:length(errors), errors, 'FaceColor', [0.3, 0.6, 0.9]);
title('定位误差分析');
xlabel('测试点编号'); ylabel('误差 (网格单位)');
grid on;

% 添加误差阈值线
hold on;
plot([0.5, length(errors)+0.5], [10, 10], 'g--', 'LineWidth', 2, 'DisplayName', '良好阈值');
plot([0.5, length(errors)+0.5], [15, 15], 'r--', 'LineWidth', 2, 'DisplayName', '可接受阈值');

% 标注误差值
for i = 1:length(errors)
    text(i, errors(i) + 0.5, sprintf('%.1f', errors(i)), ...
         'HorizontalAlignment', 'center', 'FontWeight', 'bold');
end

legend('定位误差', '良好阈值 (10)', '可接受阈值 (15)', 'Location', 'best');

sgtitle('地震定位精度测试结果', 'FontSize', 14, 'FontWeight', 'bold');
end
