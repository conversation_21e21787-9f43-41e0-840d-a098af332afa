function T = dial_3d_optimized(SpeedImage, SourcePoints, options)
%DIAL_3D_OPTIMIZED 优化版Dial算法3D实现
% 包含自适应量化、内存优化和多种策略
%
% 输入:
%   SpeedImage - 3D速度场 [nx x ny x nz]
%   SourcePoints - 源点坐标 [3 x N] (1-based索引)
%   options - 选项结构体:
%     .connectivity - 连接性: 6, 18, 或 26 (默认6)
%     .scale_factor - 量化比例因子 (默认自动计算)
%     .adaptive_scaling - 是否使用自适应量化 (默认true)
%     .memory_efficient - 是否使用内存优化模式 (默认true)
%     .bucket_size_limit - 桶大小限制 (默认50000)
%
% 输出:
%   T - 传播时间场 [nx x ny x nz]

% 默认选项
if nargin < 3, options = struct(); end
if ~isfield(options, 'connectivity'), options.connectivity = 6; end
if ~isfield(options, 'scale_factor'), options.scale_factor = []; end
if ~isfield(options, 'adaptive_scaling'), options.adaptive_scaling = true; end
if ~isfield(options, 'memory_efficient'), options.memory_efficient = true; end
if ~isfield(options, 'bucket_size_limit'), options.bucket_size_limit = 50000; end

% 获取尺寸
[nx, ny, nz] = size(SpeedImage);
num_sources = size(SourcePoints, 2);

% 计算速度统计信息
speed_stats = analyze_speed_field(SpeedImage);

% 自适应确定量化因子
if isempty(options.scale_factor)
    options.scale_factor = calculate_optimal_scale_factor(speed_stats, [nx, ny, nz]);
end

fprintf('开始优化Dial算法\n');
fprintf('配置: 连接性=%d, 量化因子=%.0f, 自适应=%d, 内存优化=%d\n', ...
    options.connectivity, options.scale_factor, options.adaptive_scaling, options.memory_efficient);

% 初始化
T = inf(nx, ny, nz);
quantized_dist = inf(nx, ny, nz);
visited = false(nx, ny, nz);

% 估计桶参数
bucket_params = estimate_bucket_parameters(speed_stats, [nx, ny, nz], options);

% 初始化桶结构
if options.memory_efficient
    % 使用循环桶减少内存使用
    buckets = initialize_circular_buckets(bucket_params);
else
    % 使用标准桶结构
    buckets = cell(bucket_params.max_buckets, 1);
end

% 定义邻居偏移和距离
[neighbors, neighbor_distances] = get_optimized_neighbors(options.connectivity);
num_neighbors = size(neighbors, 1);

% 初始化源点
current_bucket = 1;
max_bucket_used = 0;

for s = 1:num_sources
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    if sx >= 1 && sx <= nx && sy >= 1 && sy <= ny && sz >= 1 && sz <= nz
        quantized_dist(sx, sy, sz) = 0;
        T(sx, sy, sz) = 0;
        
        if options.memory_efficient
            add_to_circular_bucket(buckets, 1, [sx, sy, sz]);
        else
            buckets{1} = [buckets{1}; [sx, sy, sz]];
        end
        max_bucket_used = max(max_bucket_used, 1);
    end
end

processed_count = 0;
total_nodes = nx * ny * nz;

% 主循环
tic;
while current_bucket <= max_bucket_used
    % 处理当前桶
    while true
        if options.memory_efficient
            current_node = extract_from_circular_bucket(buckets, current_bucket);
            if isempty(current_node), break; end
        else
            if isempty(buckets{current_bucket}), break; end
            current_node = buckets{current_bucket}(1, :);
            buckets{current_bucket}(1, :) = [];
        end
        
        cx = current_node(1);
        cy = current_node(2);
        cz = current_node(3);
        
        % 跳过已访问的节点
        if visited(cx, cy, cz), continue; end
        
        % 标记为已访问
        visited(cx, cy, cz) = true;
        processed_count = processed_count + 1;
        
        % 显示进度
        if mod(processed_count, 30000) == 0
            elapsed = toc;
            rate = processed_count / elapsed;
            fprintf('进度: %d/%d (%.1f%%), 桶: %d/%d, 速度: %.0f节点/秒\n', ...
                processed_count, total_nodes, 100*processed_count/total_nodes, ...
                current_bucket, max_bucket_used, rate);
        end
        
        % 处理邻居
        for n = 1:num_neighbors
            nx_coord = cx + neighbors(n, 1);
            ny_coord = cy + neighbors(n, 2);
            nz_coord = cz + neighbors(n, 3);
            
            % 边界和访问检查
            if nx_coord < 1 || nx_coord > nx || ...
               ny_coord < 1 || ny_coord > ny || ...
               nz_coord < 1 || nz_coord > nz || ...
               visited(nx_coord, ny_coord, nz_coord)
                continue;
            end
            
            % 计算传播时间
            if options.adaptive_scaling
                [travel_time, quantized_time] = calculate_adaptive_travel_time(...
                    SpeedImage, cx, cy, cz, nx_coord, ny_coord, nz_coord, ...
                    neighbor_distances(n), options.scale_factor, speed_stats);
            else
                [travel_time, quantized_time] = calculate_standard_travel_time(...
                    SpeedImage, cx, cy, cz, nx_coord, ny_coord, nz_coord, ...
                    neighbor_distances(n), options.scale_factor);
            end
            
            new_quantized_dist = quantized_dist(cx, cy, cz) + quantized_time;
            
            % 更新距离
            if new_quantized_dist < quantized_dist(nx_coord, ny_coord, nz_coord)
                quantized_dist(nx_coord, ny_coord, nz_coord) = new_quantized_dist;
                T(nx_coord, ny_coord, nz_coord) = new_quantized_dist / options.scale_factor;
                
                % 添加到桶
                bucket_idx = new_quantized_dist + 1;
                if bucket_idx <= bucket_params.max_buckets
                    if options.memory_efficient
                        add_to_circular_bucket(buckets, bucket_idx, [nx_coord, ny_coord, nz_coord]);
                    else
                        buckets{bucket_idx} = [buckets{bucket_idx}; [nx_coord, ny_coord, nz_coord]];
                    end
                    max_bucket_used = max(max_bucket_used, bucket_idx);
                end
            end
        end
    end
    
    current_bucket = current_bucket + 1;
end

total_time = toc;
fprintf('优化Dial算法完成！\n');
fprintf('总用时: %.2f秒, 处理节点: %d, 效率: %.0f节点/秒\n', ...
    total_time, processed_count, processed_count/total_time);

end

function stats = analyze_speed_field(SpeedImage)
%ANALYZE_SPEED_FIELD 分析速度场统计信息

stats.mean = mean(SpeedImage(:));
stats.min = min(SpeedImage(:));
stats.max = max(SpeedImage(:));
stats.std = std(SpeedImage(:));
stats.range = stats.max - stats.min;
stats.cv = stats.std / stats.mean;  % 变异系数

end

function scale_factor = calculate_optimal_scale_factor(speed_stats, dims)
%CALCULATE_OPTIMAL_SCALE_FACTOR 计算最优量化因子

% 基于速度范围和网格尺寸计算
max_distance = sqrt(sum(dims.^2));
min_travel_time = 1 / speed_stats.max;
max_travel_time = max_distance / speed_stats.min;

% 目标：量化后的最大值不超过100万
target_max_quantized = 1000000;
scale_factor = target_max_quantized / max_travel_time;

% 确保最小量化步长有意义
min_scale = 100 / min_travel_time;
scale_factor = max(scale_factor, min_scale);

% 取整到合适的值
scale_factor = round(scale_factor / 100) * 100;
scale_factor = max(scale_factor, 1000);

end

function params = estimate_bucket_parameters(speed_stats, dims, options)
%ESTIMATE_BUCKET_PARAMETERS 估计桶参数

max_distance = sqrt(sum(dims.^2));
max_travel_time = max_distance / max(speed_stats.min, 1e-8);
max_quantized = ceil(max_travel_time * options.scale_factor);

params.max_buckets = min(max_quantized, options.bucket_size_limit);
params.bucket_increment = max(1, ceil(max_quantized / options.bucket_size_limit));

end

function buckets = initialize_circular_buckets(params)
%INITIALIZE_CIRCULAR_BUCKETS 初始化循环桶结构

buckets.data = cell(params.max_buckets, 1);
buckets.max_buckets = params.max_buckets;
buckets.base_offset = 0;

end

function add_to_circular_bucket(buckets, bucket_idx, node)
%ADD_TO_CIRCULAR_BUCKET 添加节点到循环桶

actual_idx = mod(bucket_idx - 1, buckets.max_buckets) + 1;
buckets.data{actual_idx} = [buckets.data{actual_idx}; node];

end

function node = extract_from_circular_bucket(buckets, bucket_idx)
%EXTRACT_FROM_CIRCULAR_BUCKET 从循环桶提取节点

actual_idx = mod(bucket_idx - 1, buckets.max_buckets) + 1;
if isempty(buckets.data{actual_idx})
    node = [];
else
    node = buckets.data{actual_idx}(1, :);
    buckets.data{actual_idx}(1, :) = [];
end

end

function [neighbors, distances] = get_optimized_neighbors(connectivity)
%GET_OPTIMIZED_NEIGHBORS 获取优化的邻居配置

switch connectivity
    case 6
        neighbors = [-1,0,0; 1,0,0; 0,-1,0; 0,1,0; 0,0,-1; 0,0,1];
        distances = ones(6, 1);
        
    case 18
        neighbors = [
            -1,0,0; 1,0,0; 0,-1,0; 0,1,0; 0,0,-1; 0,0,1;
            -1,-1,0; -1,1,0; 1,-1,0; 1,1,0;
            -1,0,-1; -1,0,1; 1,0,-1; 1,0,1;
            0,-1,-1; 0,-1,1; 0,1,-1; 0,1,1
        ];
        distances = [ones(6,1); sqrt(2)*ones(12,1)];
        
    case 26
        neighbors = [
            -1,0,0; 1,0,0; 0,-1,0; 0,1,0; 0,0,-1; 0,0,1;
            -1,-1,0; -1,1,0; 1,-1,0; 1,1,0;
            -1,0,-1; -1,0,1; 1,0,-1; 1,0,1;
            0,-1,-1; 0,-1,1; 0,1,-1; 0,1,1;
            -1,-1,-1; -1,-1,1; -1,1,-1; -1,1,1;
            1,-1,-1; 1,-1,1; 1,1,-1; 1,1,1
        ];
        distances = [ones(6,1); sqrt(2)*ones(12,1); sqrt(3)*ones(8,1)];
        
    otherwise
        error('连接性必须是 6, 18, 或 26');
end

end

function [travel_time, quantized_time] = calculate_adaptive_travel_time(...
    SpeedImage, x1, y1, z1, x2, y2, z2, distance, scale_factor, speed_stats)
%CALCULATE_ADAPTIVE_TRAVEL_TIME 自适应传播时间计算

v1 = SpeedImage(x1, y1, z1);
v2 = SpeedImage(x2, y2, z2);

% 根据速度变化选择平均方法
speed_ratio = max(v1, v2) / min(v1, v2);
if speed_ratio < 2.0
    v_avg = (v1 + v2) / 2;  % 算术平均
else
    v_avg = 2 / (1/max(v1, 1e-8) + 1/max(v2, 1e-8));  % 调和平均
end

travel_time = distance / max(v_avg, 1e-8);

% 自适应量化
local_scale = scale_factor;
if speed_stats.cv > 0.5  % 高变异性
    local_scale = scale_factor * 1.5;
end

quantized_time = round(travel_time * local_scale);

end

function [travel_time, quantized_time] = calculate_standard_travel_time(...
    SpeedImage, x1, y1, z1, x2, y2, z2, distance, scale_factor)
%CALCULATE_STANDARD_TRAVEL_TIME 标准传播时间计算

v1 = SpeedImage(x1, y1, z1);
v2 = SpeedImage(x2, y2, z2);
v_avg = 2 / (1/max(v1, 1e-8) + 1/max(v2, 1e-8));

travel_time = distance / v_avg;
quantized_time = round(travel_time * scale_factor);

end
