%% 编译Bellman-Ford算法MEX文件
% 这个脚本用于编译bellman_ford_3d.c文件并进行全面测试

fprintf('正在编译Bellman-Ford算法MEX文件...\n');

try
    % 编译MEX文件
    mex bellman_ford_3d.c
    fprintf('✓ 编译成功！\n');
    
    % 基础功能测试
    fprintf('\n=== 基础功能测试 ===\n');
    test_speed = ones([30, 30, 30]) * 1500;
    test_source = [15; 15; 15];
    
    % 测试不同连接性和最大迭代次数
    connectivities = [6, 18, 26];
    max_iterations = [10, 50, 100];
    
    fprintf('测试不同配置...\n');
    for c = 1:length(connectivities)
        conn = connectivities(c);
        for m = 1:length(max_iterations)
            max_iter = max_iterations(m);
            
            fprintf('连接性=%d, 最大迭代=%d: ', conn, max_iter);
            tic;
            try
                result = bellman_ford_3d(test_speed, test_source, conn, max_iter);
                elapsed = toc;
                
                if all(size(result) == [30, 30, 30])
                    fprintf('✓ 成功 (%.3f秒)\n', elapsed);
                else
                    fprintf('✗ 尺寸错误\n');
                end
            catch ME
                fprintf('✗ 失败: %s\n', ME.message);
            end
        end
    end
    
    % 精度验证测试
    fprintf('\n=== 精度验证测试 ===\n');
    [X, Y, Z] = ndgrid(1:30, 1:30, 1:30);
    theory = sqrt((X-15).^2 + (Y-15).^2 + (Z-15).^2) / 1500;
    
    % 测试不同迭代次数对精度的影响
    fprintf('迭代次数对精度的影响:\n');
    for m = 1:length(max_iterations)
        max_iter = max_iterations(m);
        result = bellman_ford_3d(test_speed, test_source, 6, max_iter);
        error = mean(abs(theory(:) - result(:)));
        fprintf('  最大迭代 %d: 平均误差 %.6f\n', max_iter, error);
    end
    
    % 收敛性测试
    fprintf('\n=== 收敛性测试 ===\n');
    fprintf('测试算法收敛行为...\n');
    
    % 使用更多迭代次数测试收敛
    convergence_iterations = [5, 10, 20, 50, 100];
    previous_result = [];
    
    for i = 1:length(convergence_iterations)
        max_iter = convergence_iterations(i);
        result = bellman_ford_3d(test_speed, test_source, 6, max_iter);
        
        if ~isempty(previous_result)
            diff = mean(abs(result(:) - previous_result(:)));
            fprintf('  迭代 %d vs %d: 差异 %.6f\n', ...
                convergence_iterations(i-1), max_iter, diff);
            
            if diff < 1e-10
                fprintf('  ✓ 算法在 %d 次迭代后收敛\n', max_iter);
                break;
            end
        end
        
        previous_result = result;
    end
    
    % 与MATLAB版本比较
    fprintf('\n=== 与MATLAB版本比较 ===\n');
    try
        result_c = bellman_ford_3d(test_speed, test_source, 6, 50);
        result_matlab = bellman_ford_3d_matlab(test_speed, test_source, 6, 50);
        
        diff_versions = mean(abs(result_c(:) - result_matlab(:)));
        fprintf('C版本与MATLAB版本差异: %.6f\n', diff_versions);
        
        if diff_versions < 1e-6
            fprintf('✓ C版本与MATLAB版本结果基本一致\n');
        else
            fprintf('⚠ C版本与MATLAB版本存在差异\n');
        end
    catch
        fprintf('MATLAB版本不可用，跳过比较\n');
    end
    
    % 负权重边测试
    fprintf('\n=== 负权重边测试 ===\n');
    
    % 创建包含"负速度"区域的测试场景
    % 注意：在物理上速度不能为负，但我们可以测试算法的鲁棒性
    negative_speed = ones([20, 20, 20]) * 1500;
    negative_speed(8:12, 8:12, 8:12) = -500;  % 负速度区域（非物理）
    
    negative_source = [10; 10; 10];
    
    fprintf('测试包含负权重的场景...\n');
    tic;
    try
        result_negative = bellman_ford_3d(abs(negative_speed), negative_source, 6, 30);
        elapsed_negative = toc;
        
        finite_ratio = sum(isfinite(result_negative(:))) / numel(result_negative);
        fprintf('负权重测试: %.3f秒, 有限值比例: %.1f%%\n', elapsed_negative, finite_ratio * 100);
        
        if finite_ratio > 0.8
            fprintf('✓ 算法在负权重场景中表现稳定\n');
        else
            fprintf('⚠ 算法在负权重场景中可能不稳定\n');
        end
    catch ME
        fprintf('✗ 负权重测试失败: %s\n', ME.message);
    end
    
    % 性能基准测试
    fprintf('\n=== 性能基准测试 ===\n');
    sizes = [20, 30, 40, 50];
    
    for s = 1:length(sizes)
        sz = sizes(s);
        test_vol = ones([sz, sz, sz]) * 1500;
        test_src = [round(sz/2); round(sz/2); round(sz/2)];
        
        fprintf('\n网格尺寸: %dx%dx%d (%d个体素)\n', sz, sz, sz, sz^3);
        
        % 测试不同连接性的性能
        for c = 1:length(connectivities)
            conn = connectivities(c);
            fprintf('  连接性 %d: ', conn);
            
            % 使用适当的迭代次数
            max_iter = min(50, sz);
            
            tic;
            try
                result = bellman_ford_3d(test_vol, test_src, conn, max_iter);
                elapsed = toc;
                rate = sz^3 / elapsed;
                fprintf('%.3f秒 (%.0f体素/秒)\n', elapsed, rate);
            catch ME
                fprintf('失败 (%s)\n', ME.message);
            end
        end
    end
    
    % 迭代次数性能影响测试
    fprintf('\n=== 迭代次数性能影响测试 ===\n');
    test_vol = ones([40, 40, 40]) * 1500;
    test_src = [20; 20; 20];
    
    iteration_counts = [5, 10, 20, 50, 100];
    fprintf('测试不同迭代次数对性能的影响:\n');
    
    for i = 1:length(iteration_counts)
        max_iter = iteration_counts(i);
        fprintf('迭代次数 %d: ', max_iter);
        
        tic;
        try
            result = bellman_ford_3d(test_vol, test_src, 6, max_iter);
            elapsed = toc;
            
            % 计算精度
            [X, Y, Z] = ndgrid(1:40, 1:40, 1:40);
            theory = sqrt((X-20).^2 + (Y-20).^2 + (Z-20).^2) / 1500;
            error = mean(abs(theory(:) - result(:)));
            
            fprintf('%.3f秒, 误差=%.6f\n', elapsed, error);
        catch ME
            fprintf('失败 (%s)\n', ME.message);
        end
    end
    
    % 与其他算法比较
    fprintf('\n=== 与其他算法比较 ===\n');
    comp_vol = ones([50, 50, 50]) * 1500;
    comp_src = [25; 25; 25];
    
    % Bellman-Ford算法
    fprintf('Bellman-Ford算法: ');
    tic;
    result_bf = bellman_ford_3d(comp_vol, comp_src, 6, 30);
    time_bf = toc;
    fprintf('%.3f秒\n', time_bf);
    
    % 与其他算法比较 (如果可用)
    algorithms = {'dijkstra_3d', 'astar_3d', 'dial_3d', 'dag_shortest_path_3d'};
    algorithm_names = {'Dijkstra', 'A*', 'Dial', 'DAG'};
    algorithm_params = {[6], [6, 1.0], [6, 1000], [6, 0]};
    
    for a = 1:length(algorithms)
        if exist(algorithms{a}, 'file') == 3
            fprintf('%s算法: ', algorithm_names{a});
            tic;
            try
                switch a
                    case 1  % Dijkstra
                        result_other = dijkstra_3d(comp_vol, comp_src, algorithm_params{a}(1));
                    case 2  % A*
                        result_other = astar_3d(comp_vol, comp_src, algorithm_params{a}(1), algorithm_params{a}(2));
                    case 3  % Dial
                        result_other = dial_3d(comp_vol, comp_src, algorithm_params{a}(1), algorithm_params{a}(2));
                    case 4  % DAG
                        result_other = dag_shortest_path_3d(comp_vol, comp_src, algorithm_params{a}(1), algorithm_params{a}(2));
                end
                time_other = toc;
                fprintf('%.3f秒\n', time_other);
                
                % 比较结果差异
                diff = mean(abs(result_bf(:) - result_other(:)));
                fprintf('  与Bellman-Ford差异: %.6f\n', diff);
                
                % 速度比较
                if time_other < time_bf
                    speedup = time_bf / time_other;
                    fprintf('  %s加速比: %.2fx\n', algorithm_names{a}, speedup);
                else
                    slowdown = time_other / time_bf;
                    fprintf('  Bellman-Ford加速比: %.2fx\n', slowdown);
                end
            catch ME
                fprintf('失败 (%s)\n', ME.message);
            end
        else
            fprintf('%s算法MEX文件不可用\n', algorithm_names{a});
        end
    end
    
    % 边数量影响测试
    fprintf('\n=== 边数量影响测试 ===\n');
    fprintf('测试不同连接性对边数量和性能的影响...\n');
    
    test_vol = ones([30, 30, 30]) * 1500;
    test_src = [15; 15; 15];
    
    for c = 1:length(connectivities)
        conn = connectivities(c);
        
        % 估算边数量
        total_nodes = 30^3;
        avg_neighbors = conn * (1 - 6/(30^3));  % 考虑边界效应
        estimated_edges = total_nodes * avg_neighbors;
        
        fprintf('连接性 %d: ', conn);
        fprintf('估算边数 %.0f, ', estimated_edges);
        
        tic;
        result = bellman_ford_3d(test_vol, test_src, conn, 20);
        elapsed = toc;
        
        fprintf('用时 %.3f秒\n', elapsed);
    end
    
    fprintf('\n✓ Bellman-Ford算法MEX文件测试完成！\n');
    
catch ME
    fprintf('✗ 编译或测试失败: %s\n', ME.message);
    fprintf('\n可能的解决方案:\n');
    fprintf('1. 确保已安装MATLAB编译器 (运行: mex -setup)\n');
    fprintf('2. 检查bellman_ford_3d.c文件是否存在\n');
    fprintf('3. 确保C编译器已正确配置\n');
    fprintf('4. 检查系统内存是否足够\n');
    fprintf('5. 确保边列表构建正确\n');
end

%% 使用建议和最佳实践
fprintf('\n=== Bellman-Ford算法使用建议 ===\n');
fprintf('1. 迭代次数选择:\n');
fprintf('   - 小网格 (<50^3): 10-30次迭代\n');
fprintf('   - 中等网格 (50-100^3): 30-50次迭代\n');
fprintf('   - 大网格 (>100^3): 50-100次迭代\n');
fprintf('   - 理论最大: V-1次 (通常不需要)\n\n');

fprintf('2. 连接性选择:\n');
fprintf('   - 6-连通: 最快，适合规则结构\n');
fprintf('   - 18-连通: 平衡精度和速度\n');
fprintf('   - 26-连通: 最高精度，计算量大\n\n');

fprintf('3. 适用场景:\n');
fprintf('   - 需要负权重处理: 唯一选择\n');
fprintf('   - 稳定性要求高: 理想选择\n');
fprintf('   - 简单实现需求: 算法逻辑简单\n');
fprintf('   - 负环检测: 内置支持\n\n');

fprintf('4. 性能优化:\n');
fprintf('   - 使用SPFA优化版本\n');
fprintf('   - 根据收敛情况调整迭代次数\n');
fprintf('   - 对于正权重，考虑Dijkstra\n');
fprintf('   - 大网格使用较低连接性\n\n');

fprintf('5. 注意事项:\n');
fprintf('   - 时间复杂度较高 O(VE)\n');
fprintf('   - 适合稠密图或需要负权重处理\n');
fprintf('   - 可以检测负环（在传播时间中很少见）\n');
fprintf('   - 实现简单，数值稳定\n');
