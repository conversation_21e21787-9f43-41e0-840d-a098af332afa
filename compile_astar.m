%% 编译A*算法MEX文件
% 这个脚本用于编译astar_3d.c文件并进行性能测试

fprintf('正在编译A*算法MEX文件...\n');

try
    % 编译MEX文件
    mex astar_3d.c
    fprintf('✓ 编译成功！\n');
    
    % 基础功能测试
    fprintf('\n=== 基础功能测试 ===\n');
    test_speed = ones([30, 30, 30]) * 1500;
    test_source = [15; 15; 15];
    
    % 测试不同连接性
    connectivities = [6, 18, 26];
    heuristic_weights = [0.5, 1.0, 1.5];
    
    fprintf('测试不同配置...\n');
    for c = 1:length(connectivities)
        conn = connectivities(c);
        for h = 1:length(heuristic_weights)
            hw = heuristic_weights(h);
            
            fprintf('连接性=%d, 启发式权重=%.1f: ', conn, hw);
            tic;
            try
                result = astar_3d(test_speed, test_source, conn, hw);
                elapsed = toc;
                
                if all(size(result) == [30, 30, 30])
                    fprintf('✓ 成功 (%.3f秒)\n', elapsed);
                else
                    fprintf('✗ 尺寸错误\n');
                end
            catch ME
                fprintf('✗ 失败: %s\n', ME.message);
            end
        end
    end
    
    % 精度验证测试
    fprintf('\n=== 精度验证测试 ===\n');
    [X, Y, Z] = ndgrid(1:30, 1:30, 1:30);
    theory = sqrt((X-15).^2 + (Y-15).^2 + (Z-15).^2) / 1500;
    
    % 测试A*与理论解的差异
    result_astar = astar_3d(test_speed, test_source, 26, 1.0);
    error_astar = mean(abs(theory(:) - result_astar(:)));
    fprintf('A*算法 (26-连通) 平均误差: %.6f\n', error_astar);
    
    % 与MATLAB版本比较 (如果可用)
    try
        result_matlab = astar_3d_matlab(test_speed, test_source, 26, 1.0);
        diff_versions = mean(abs(result_astar(:) - result_matlab(:)));
        fprintf('C版本与MATLAB版本差异: %.6f\n', diff_versions);
        
        if diff_versions < 1e-10
            fprintf('✓ C版本与MATLAB版本结果一致\n');
        else
            fprintf('⚠ C版本与MATLAB版本存在差异\n');
        end
    catch
        fprintf('MATLAB版本不可用，跳过比较\n');
    end
    
    % 性能基准测试
    fprintf('\n=== 性能基准测试 ===\n');
    sizes = [20, 40, 60];
    
    for s = 1:length(sizes)
        sz = sizes(s);
        test_vol = ones([sz, sz, sz]) * 1500;
        test_src = [round(sz/2); round(sz/2); round(sz/2)];
        
        fprintf('\n网格尺寸: %dx%dx%d (%d个体素)\n', sz, sz, sz, sz^3);
        
        % 测试不同连接性的性能
        for c = 1:length(connectivities)
            conn = connectivities(c);
            fprintf('  连接性 %d: ', conn);
            
            tic;
            try
                result = astar_3d(test_vol, test_src, conn, 1.0);
                elapsed = toc;
                rate = sz^3 / elapsed;
                fprintf('%.3f秒 (%.0f体素/秒)\n', elapsed, rate);
            catch ME
                fprintf('失败 (%s)\n', ME.message);
            end
        end
    end
    
    % 启发式权重影响测试
    fprintf('\n=== 启发式权重影响测试 ===\n');
    test_vol = ones([40, 40, 40]) * 1500;
    test_src = [20; 20; 20];
    
    weights = [0.0, 0.5, 1.0, 1.5, 2.0];
    fprintf('测试不同启发式权重对性能的影响:\n');
    
    for w = 1:length(weights)
        weight = weights(w);
        fprintf('权重 %.1f: ', weight);
        
        tic;
        try
            result = astar_3d(test_vol, test_src, 6, weight);
            elapsed = toc;
            
            % 计算精度
            [X, Y, Z] = ndgrid(1:40, 1:40, 1:40);
            theory = sqrt((X-20).^2 + (Y-20).^2 + (Z-20).^2) / 1500;
            error = mean(abs(theory(:) - result(:)));
            
            fprintf('%.3f秒, 误差=%.6f\n', elapsed, error);
        catch ME
            fprintf('失败 (%s)\n', ME.message);
        end
    end
    
    % 与其他算法比较 (如果可用)
    fprintf('\n=== 与其他算法比较 ===\n');
    comp_vol = ones([50, 50, 50]) * 1500;
    comp_src = [25; 25; 25];
    
    % A*算法
    fprintf('A*算法: ');
    tic;
    result_astar = astar_3d(comp_vol, comp_src, 6, 1.0);
    time_astar = toc;
    fprintf('%.3f秒\n', time_astar);
    
    % Dijkstra算法 (如果可用)
    if exist('dijkstra_3d', 'file') == 3
        fprintf('Dijkstra算法: ');
        tic;
        result_dijkstra = dijkstra_3d(comp_vol, comp_src, 6);
        time_dijkstra = toc;
        fprintf('%.3f秒\n', time_dijkstra);
        
        % 比较结果差异
        diff = mean(abs(result_astar(:) - result_dijkstra(:)));
        fprintf('A*与Dijkstra结果差异: %.6f\n', diff);
        
        % 速度比较
        speedup = time_dijkstra / time_astar;
        fprintf('A*相对Dijkstra加速比: %.2fx\n', speedup);
    else
        fprintf('Dijkstra算法MEX文件不可用\n');
    end
    
    fprintf('\n✓ A*算法MEX文件测试完成！\n');
    
catch ME
    fprintf('✗ 编译或测试失败: %s\n', ME.message);
    fprintf('\n可能的解决方案:\n');
    fprintf('1. 确保已安装MATLAB编译器 (运行: mex -setup)\n');
    fprintf('2. 检查astar_3d.c文件是否存在\n');
    fprintf('3. 确保C编译器已正确配置\n');
    fprintf('4. 检查系统内存是否足够\n');
end

%% 使用建议
fprintf('\n=== A*算法使用建议 ===\n');
fprintf('1. 连接性选择:\n');
fprintf('   - 6-连通: 最快，适合实时应用\n');
fprintf('   - 18-连通: 平衡精度和速度\n');
fprintf('   - 26-连通: 最高精度，计算量大\n\n');

fprintf('2. 启发式权重选择:\n');
fprintf('   - 0.0: 退化为Dijkstra算法\n');
fprintf('   - 1.0: 标准A*，理论最优\n');
fprintf('   - >1.0: 加权A*，更快但可能不是最优解\n\n');

fprintf('3. 性能优化:\n');
fprintf('   - 大网格: 使用较小的启发式权重\n');
fprintf('   - 实时应用: 使用6-连通和权重1.5\n');
fprintf('   - 高精度: 使用26-连通和权重1.0\n');
