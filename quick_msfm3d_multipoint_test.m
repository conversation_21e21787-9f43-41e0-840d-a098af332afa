% MSFM3D 快速多点定位测试
% 简化版本，用于快速验证多点定位性能

clear; clc; close all;

fprintf('=== MSFM3D 快速多点定位测试 ===\n\n');

%% 1. 创建简化的3D速度模型
fprintf('1. 创建3D速度模型...\n');

% 小尺寸模型以加快计算
nx = 50; ny = 50; nz = 25;
SpeedImage3D = zeros(nx, ny, nz);

% 简单分层模型
for k = 1:nz
    if k <= 5
        SpeedImage3D(:, :, k) = 2000;      % 表层
    elseif k <= 10
        SpeedImage3D(:, :, k) = 3000;      % 第二层
    elseif k <= 15
        SpeedImage3D(:, :, k) = 4000;      % 第三层
    elseif k <= 20
        SpeedImage3D(:, :, k) = 5000;      % 第四层
    else
        SpeedImage3D(:, :, k) = 6000;      % 底层
    end
end

% 添加一个低速异常体
SpeedImage3D(20:25, 20:25, 10:15) = 1500;

fprintf('   模型尺寸: %d × %d × %d\n', nx, ny, nz);

%% 2. 设置台站网络
stations = [5, 5, 1;      % 台站1
           45, 5, 1;     % 台站2
           45, 45, 1;    % 台站3
           5, 45, 1;     % 台站4
           25, 25, 1];   % 台站5 (中心)

num_stations = size(stations, 1);
fprintf('   台站数量: %d\n', num_stations);

%% 3. 设置10个测试震源
test_sources = [
    15, 15, 8;    % 1. 浅层左下
    35, 15, 8;    % 2. 浅层右下
    35, 35, 8;    % 3. 浅层右上
    15, 35, 8;    % 4. 浅层左上
    25, 25, 8;    % 5. 浅层中心
    20, 20, 12;   % 6. 中层
    30, 30, 12;   % 7. 中层
    25, 20, 18;   % 8. 深层
    20, 30, 18;   % 9. 深层
    25, 25, 15    % 10. 中心深层
];

num_tests = size(test_sources, 1);
fprintf('   测试震源数量: %d\n', num_tests);

%% 4. 计算走时表
fprintf('\n2. 计算走时表...\n');
travel_time_tables = cell(num_stations, 1);

for i = 1:num_stations
    tic;
    fprintf('   台站 %d...', i);
    travel_time_tables{i} = msfm3d(SpeedImage3D, stations(i,:)', true, true);
    fprintf(' 完成 (%.2f秒)\n', toc);
end

%% 5. 执行多点定位测试
fprintf('\n3. 执行多点定位测试...\n');

location_errors = zeros(num_tests, 1);
estimated_sources = zeros(num_tests, 3);
residuals = zeros(num_tests, 1);
computation_times = zeros(num_tests, 1);

% 搜索参数
search_range_x = 8:2:42;
search_range_y = 8:2:42;
search_range_z = 5:2:20;

fprintf('   搜索范围: X[%d:2:%d], Y[%d:2:%d], Z[%d:2:%d]\n', ...
    search_range_x(1), search_range_x(end), ...
    search_range_y(1), search_range_y(end), ...
    search_range_z(1), search_range_z(end));

total_search_points = length(search_range_x) * length(search_range_y) * length(search_range_z);
fprintf('   每次搜索点数: %d\n', total_search_points);

for test_idx = 1:num_tests
    true_source = test_sources(test_idx, :);
    fprintf('\n测试点 %d: [%d, %d, %d] -> ', test_idx, true_source(1), true_source(2), true_source(3));
    
    % 生成观测数据
    observed_times = zeros(num_stations, 1);
    noise_level = 0.001; % 1ms噪声
    
    for i = 1:num_stations
        observed_times(i) = travel_time_tables{i}(true_source(1), true_source(2), true_source(3));
        observed_times(i) = observed_times(i) + noise_level * randn();
    end
    
    % 网格搜索定位
    tic;
    min_residual = inf;
    best_location = [0, 0, 0];
    
    for i = 1:length(search_range_x)
        for j = 1:length(search_range_y)
            for k = 1:length(search_range_z)
                x = search_range_x(i);
                y = search_range_y(j);
                z = search_range_z(k);
                
                % 计算理论走时
                theoretical_times = zeros(num_stations, 1);
                for s = 1:num_stations
                    theoretical_times(s) = travel_time_tables{s}(x, y, z);
                end
                
                % 计算残差
                residual = sqrt(mean((observed_times - theoretical_times).^2));
                
                if residual < min_residual
                    min_residual = residual;
                    best_location = [x, y, z];
                end
            end
        end
    end
    
    search_time = toc;
    
    % 计算误差
    error = norm(true_source - best_location);
    
    % 存储结果
    location_errors(test_idx) = error;
    estimated_sources(test_idx, :) = best_location;
    residuals(test_idx) = min_residual;
    computation_times(test_idx) = search_time;
    
    fprintf('[%d, %d, %d], 误差: %.2f, 时间: %.2f秒\n', ...
        best_location(1), best_location(2), best_location(3), error, search_time);
end

%% 6. 统计分析
fprintf('\n=== 统计结果 ===\n');
fprintf('定位误差统计:\n');
fprintf('  平均误差: %.2f 网格单位\n', mean(location_errors));
fprintf('  误差标准差: %.2f 网格单位\n', std(location_errors));
fprintf('  最大误差: %.2f 网格单位\n', max(location_errors));
fprintf('  最小误差: %.2f 网格单位\n', min(location_errors));
fprintf('  RMS误差: %.2f 网格单位\n', sqrt(mean(location_errors.^2)));

fprintf('\n走时残差统计:\n');
fprintf('  平均残差: %.6f 秒 (%.2f ms)\n', mean(residuals), mean(residuals)*1000);
fprintf('  残差标准差: %.6f 秒\n', std(residuals));

fprintf('\n计算时间统计:\n');
fprintf('  平均时间: %.2f 秒\n', mean(computation_times));
fprintf('  总时间: %.2f 秒\n', sum(computation_times));

%% 7. 可视化
fprintf('\n4. 生成可视化...\n');

figure(1);
set(gcf, 'Position', [100, 100, 1400, 900]);

% 3D散点图
subplot(2, 3, 1);
scatter3(stations(:,1), stations(:,2), stations(:,3), 150, '^k', 'filled');
hold on;

colors = lines(num_tests);
for i = 1:num_tests
    scatter3(test_sources(i,1), test_sources(i,2), test_sources(i,3), ...
        200, colors(i,:), '*', 'LineWidth', 2);
    scatter3(estimated_sources(i,1), estimated_sources(i,2), estimated_sources(i,3), ...
        100, colors(i,:), 'o', 'LineWidth', 2);
    plot3([test_sources(i,1), estimated_sources(i,1)], ...
          [test_sources(i,2), estimated_sources(i,2)], ...
          [test_sources(i,3), estimated_sources(i,3)], ...
          '--', 'Color', colors(i,:), 'LineWidth', 1);
end

xlabel('X'); ylabel('Y'); zlabel('Z');
title('3D多点定位结果');
legend('台站', '真实震源', '定位结果', 'Location', 'best');
grid on;

% 定位误差
subplot(2, 3, 2);
bar(1:num_tests, location_errors);
title('定位误差');
xlabel('测试点'); ylabel('误差 (网格单位)');
grid on;
mean_error = mean(location_errors);
hold on;
plot([0.5, num_tests+0.5], [mean_error, mean_error], 'r--', 'LineWidth', 2);

% 走时残差
subplot(2, 3, 3);
bar(1:num_tests, residuals*1000); % 转换为毫秒
title('走时残差');
xlabel('测试点'); ylabel('残差 (毫秒)');
grid on;

% 计算时间
subplot(2, 3, 4);
bar(1:num_tests, computation_times);
title('计算时间');
xlabel('测试点'); ylabel('时间 (秒)');
grid on;

% XY平面投影
subplot(2, 3, 5);
slice_z = 10;
pcolor(squeeze(SpeedImage3D(:,:,slice_z))); 
shading interp; colormap(jet); colorbar;
hold on;
plot(stations(:,2), stations(:,1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
for i = 1:num_tests
    plot(test_sources(i,2), test_sources(i,1), '*', 'Color', colors(i,:), 'MarkerSize', 12);
    plot(estimated_sources(i,2), estimated_sources(i,1), 'o', 'Color', colors(i,:), 'MarkerSize', 8);
    text(test_sources(i,2)+1, test_sources(i,1)+1, num2str(i), 'FontSize', 8, 'Color', colors(i,:));
end
title(sprintf('XY投影 (Z=%d)', slice_z));
xlabel('Y'); ylabel('X');

% 误差统计
subplot(2, 3, 6);
histogram(location_errors, 'BinWidth', 0.5);
title('误差分布');
xlabel('误差 (网格单位)'); ylabel('频次');
grid on;

%% 8. 输出详细结果表格
fprintf('\n=== 详细结果 ===\n');
fprintf('测试点\t真实位置\t\t定位结果\t\t误差\t残差(ms)\t时间(s)\n');
fprintf('------\t--------\t\t--------\t\t----\t-------\t-------\n');
for i = 1:num_tests
    fprintf('%d\t[%2d,%2d,%2d]\t\t[%2d,%2d,%2d]\t\t%.2f\t%.3f\t\t%.2f\n', ...
        i, test_sources(i,1), test_sources(i,2), test_sources(i,3), ...
        estimated_sources(i,1), estimated_sources(i,2), estimated_sources(i,3), ...
        location_errors(i), residuals(i)*1000, computation_times(i));
end

fprintf('\n测试完成！\n');
