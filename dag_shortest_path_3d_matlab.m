function T = dag_shortest_path_3d_matlab(SpeedImage, SourcePoints, connectivity, layer_direction)
%DAG_SHORTEST_PATH_3D_MATLAB DAG最短路径算法的3D实现
% 使用有向无环图最短路径算法计算3D网格中的传播时间
% 通过拓扑排序避免优先队列开销，适合分层结构
%
% 输入:
%   SpeedImage - 3D速度场 [nx x ny x nz]
%   SourcePoints - 源点坐标 [3 x N] (1-based索引)
%   connectivity - 连接性: 6, 18, 或 26 (默认6)
%   layer_direction - 分层方向: 0=自动, 1=X, 2=Y, 3=Z (默认0)
%
% 输出:
%   T - 传播时间场 [nx x ny x nz]

if nargin < 3, connectivity = 6; end
if nargin < 4, layer_direction = 0; end

% 获取尺寸
[nx, ny, nz] = size(SpeedImage);
num_sources = size(SourcePoints, 2);

% 自动确定最优分层方向
if layer_direction == 0
    layer_direction = determine_optimal_layer_direction(SpeedImage);
end

fprintf('开始DAG最短路径算法，网格大小: %dx%dx%d\n', nx, ny, nz);
fprintf('分层方向: %s\n', get_direction_name(layer_direction));

% 初始化
T = inf(nx, ny, nz);
processed = false(nx, ny, nz);

% 计算所有节点的层级
layer_levels = calculate_layer_levels(nx, ny, nz, layer_direction);
max_layer = max(layer_levels(:));

% 定义邻居偏移
neighbors = get_neighbor_offsets(connectivity);
num_neighbors = size(neighbors, 1);

% 计算入度（用于拓扑排序）
in_degree = zeros(nx, ny, nz);

fprintf('计算图结构和入度...\n');
for x = 1:nx
    for y = 1:ny
        for z = 1:nz
            current_layer = layer_levels(x, y, z);
            
            % 检查所有邻居
            for n = 1:num_neighbors
                nx_coord = x + neighbors(n, 1);
                ny_coord = y + neighbors(n, 2);
                nz_coord = z + neighbors(n, 3);
                
                % 边界检查
                if nx_coord >= 1 && nx_coord <= nx && ...
                   ny_coord >= 1 && ny_coord <= ny && ...
                   nz_coord >= 1 && nz_coord <= nz
                    
                    neighbor_layer = layer_levels(nx_coord, ny_coord, nz_coord);
                    
                    % 只计算不会产生环的边
                    if ~creates_cycle(current_layer, neighbor_layer)
                        in_degree(nx_coord, ny_coord, nz_coord) = ...
                            in_degree(nx_coord, ny_coord, nz_coord) + 1;
                    end
                end
            end
        end
    end
end

% 初始化拓扑排序队列
topo_queue = [];

% 初始化源点
for s = 1:num_sources
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    if sx >= 1 && sx <= nx && sy >= 1 && sy <= ny && sz >= 1 && sz <= nz
        T(sx, sy, sz) = 0;
        topo_queue = [topo_queue; [sx, sy, sz]];
    end
end

% 将所有入度为0的节点加入队列
for x = 1:nx
    for y = 1:ny
        for z = 1:nz
            if in_degree(x, y, z) == 0 && T(x, y, z) == inf
                topo_queue = [topo_queue; [x, y, z]];
            end
        end
    end
end

fprintf('开始拓扑排序和最短路径计算...\n');
processed_count = 0;
total_nodes = nx * ny * nz;

% DAG最短路径主循环
tic;
while ~isempty(topo_queue)
    % 从队列中取出一个节点
    current = topo_queue(1, :);
    topo_queue(1, :) = [];
    
    cx = current(1);
    cy = current(2);
    cz = current(3);
    
    % 跳过已处理的节点
    if processed(cx, cy, cz)
        continue;
    end
    
    % 标记为已处理
    processed(cx, cy, cz) = true;
    processed_count = processed_count + 1;
    
    % 显示进度
    if mod(processed_count, 50000) == 0
        elapsed = toc;
        rate = processed_count / elapsed;
        remaining = (total_nodes - processed_count) / rate;
        fprintf('进度: %d/%d (%.1f%%), 速度: %.0f节点/秒, 预计剩余: %.1f秒\n', ...
            processed_count, total_nodes, 100*processed_count/total_nodes, rate, remaining);
    end
    
    current_layer = layer_levels(cx, cy, cz);
    
    % 处理所有邻居
    for n = 1:num_neighbors
        nx_coord = cx + neighbors(n, 1);
        ny_coord = cy + neighbors(n, 2);
        nz_coord = cz + neighbors(n, 3);
        
        % 边界检查
        if nx_coord < 1 || nx_coord > nx || ...
           ny_coord < 1 || ny_coord > ny || ...
           nz_coord < 1 || nz_coord > nz
            continue;
        end
        
        neighbor_layer = layer_levels(nx_coord, ny_coord, nz_coord);
        
        % 跳过会产生环的边
        if creates_cycle(current_layer, neighbor_layer)
            continue;
        end
        
        % 跳过已处理的邻居
        if processed(nx_coord, ny_coord, nz_coord)
            continue;
        end
        
        % 计算传播时间
        travel_time = calculate_travel_time_dag(SpeedImage, ...
            cx, cy, cz, nx_coord, ny_coord, nz_coord);
        new_distance = T(cx, cy, cz) + travel_time;
        
        % 更新距离
        if new_distance < T(nx_coord, ny_coord, nz_coord)
            T(nx_coord, ny_coord, nz_coord) = new_distance;
        end
        
        % 减少入度，如果变为0则加入队列
        in_degree(nx_coord, ny_coord, nz_coord) = ...
            in_degree(nx_coord, ny_coord, nz_coord) - 1;
        
        if in_degree(nx_coord, ny_coord, nz_coord) == 0
            topo_queue = [topo_queue; [nx_coord, ny_coord, nz_coord]];
        end
    end
end

total_time = toc;
fprintf('DAG最短路径算法完成！\n');
fprintf('总用时: %.2f秒, 处理节点: %d, 平均速度: %.0f节点/秒\n', ...
    total_time, processed_count, processed_count/total_time);

% 检查是否存在环
remaining_nodes = sum(in_degree(:) > 0);
if remaining_nodes > 0
    fprintf('警告: 检测到 %d 个节点未处理，可能存在环\n', remaining_nodes);
end

end

function direction = determine_optimal_layer_direction(SpeedImage)
%DETERMINE_OPTIMAL_LAYER_DIRECTION 确定最优分层方向

[nx, ny, nz] = size(SpeedImage);

% 计算各方向的速度变化
var_x = calculate_directional_variance(SpeedImage, 1);
var_y = calculate_directional_variance(SpeedImage, 2);
var_z = calculate_directional_variance(SpeedImage, 3);

fprintf('方向变化分析: X=%.4f, Y=%.4f, Z=%.4f\n', var_x, var_y, var_z);

% 选择变化最大的方向（最有结构性）
[~, direction] = max([var_x, var_y, var_z]);

end

function variance = calculate_directional_variance(SpeedImage, direction)
%CALCULATE_DIRECTIONAL_VARIANCE 计算指定方向的速度变化

[nx, ny, nz] = size(SpeedImage);
differences = [];

switch direction
    case 1  % X方向
        for y = 1:ny
            for z = 1:nz
                for x = 1:nx-1
                    diff = SpeedImage(x+1, y, z) - SpeedImage(x, y, z);
                    differences = [differences; diff];
                end
            end
        end
        
    case 2  % Y方向
        for x = 1:nx
            for z = 1:nz
                for y = 1:ny-1
                    diff = SpeedImage(x, y+1, z) - SpeedImage(x, y, z);
                    differences = [differences; diff];
                end
            end
        end
        
    case 3  % Z方向
        for x = 1:nx
            for y = 1:ny
                for z = 1:nz-1
                    diff = SpeedImage(x, y, z+1) - SpeedImage(x, y, z);
                    differences = [differences; diff];
                end
            end
        end
end

variance = var(differences);

end

function layer_levels = calculate_layer_levels(nx, ny, nz, layer_direction)
%CALCULATE_LAYER_LEVELS 计算所有节点的层级

layer_levels = zeros(nx, ny, nz);

switch layer_direction
    case 1  % X方向
        for x = 1:nx
            layer_levels(x, :, :) = x;
        end
        
    case 2  % Y方向
        for y = 1:ny
            layer_levels(:, y, :) = y;
        end
        
    case 3  % Z方向
        for z = 1:nz
            layer_levels(:, :, z) = z;
        end
        
    otherwise  % 径向距离
        [X, Y, Z] = ndgrid(1:nx, 1:ny, 1:nz);
        center_x = (nx + 1) / 2;
        center_y = (ny + 1) / 2;
        center_z = (nz + 1) / 2;
        layer_levels = round(sqrt((X - center_x).^2 + (Y - center_y).^2 + (Z - center_z).^2));
end

end

function neighbors = get_neighbor_offsets(connectivity)
%GET_NEIGHBOR_OFFSETS 获取邻居偏移量

switch connectivity
    case 6
        neighbors = [-1,0,0; 1,0,0; 0,-1,0; 0,1,0; 0,0,-1; 0,0,1];
        
    case 18
        neighbors = [
            -1,0,0; 1,0,0; 0,-1,0; 0,1,0; 0,0,-1; 0,0,1;
            -1,-1,0; -1,1,0; 1,-1,0; 1,1,0;
            -1,0,-1; -1,0,1; 1,0,-1; 1,0,1;
            0,-1,-1; 0,-1,1; 0,1,-1; 0,1,1
        ];
        
    case 26
        neighbors = [
            -1,0,0; 1,0,0; 0,-1,0; 0,1,0; 0,0,-1; 0,0,1;
            -1,-1,0; -1,1,0; 1,-1,0; 1,1,0;
            -1,0,-1; -1,0,1; 1,0,-1; 1,0,1;
            0,-1,-1; 0,-1,1; 0,1,-1; 0,1,1;
            -1,-1,-1; -1,-1,1; -1,1,-1; -1,1,1;
            1,-1,-1; 1,-1,1; 1,1,-1; 1,1,1
        ];
        
    otherwise
        error('连接性必须是 6, 18, 或 26');
end

end

function is_cycle = creates_cycle(from_layer, to_layer)
%CREATES_CYCLE 检查边是否会产生环

% 在DAG中，边只能指向更高层级的节点
is_cycle = to_layer < from_layer;

end

function travel_time = calculate_travel_time_dag(SpeedImage, x1, y1, z1, x2, y2, z2)
%CALCULATE_TRAVEL_TIME_DAG 计算传播时间

v1 = SpeedImage(x1, y1, z1);
v2 = SpeedImage(x2, y2, z2);

% 使用调和平均速度
v_avg = 2 / (1/max(v1, 1e-8) + 1/max(v2, 1e-8));

% 计算欧几里得距离
dx = x2 - x1;
dy = y2 - y1;
dz = z2 - z1;
distance = sqrt(dx^2 + dy^2 + dz^2);

travel_time = distance / v_avg;

end

function name = get_direction_name(direction)
%GET_DIRECTION_NAME 获取方向名称

switch direction
    case 1
        name = 'X方向';
    case 2
        name = 'Y方向';
    case 3
        name = 'Z方向';
    otherwise
        name = '径向';
end

end
