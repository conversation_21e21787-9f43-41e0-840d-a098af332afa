function T = dijkstra_3d_optimized(SpeedImage, SourcePoints, connectivity)
%DIJKSTRA_3D_OPTIMIZED 优化版Dijkstra算法3D实现
% 使用更高效的数据结构和算法优化
%
% 输入:
%   SpeedImage - 3D速度场 [nx x ny x nz]
%   SourcePoints - 源点坐标 [3 x N] (1-based索引)
%   connectivity - 连接性: 6, 18, 或 26 (默认6)
%
% 输出:
%   T - 传播时间场 [nx x ny x nz]

if nargin < 3
    connectivity = 6;
end

% 获取尺寸
[nx, ny, nz] = size(SpeedImage);
num_sources = size(SourcePoints, 2);

% 初始化
T = inf(nx, ny, nz);
visited = false(nx, ny, nz);

% 线性索引转换函数
sub2ind_fast = @(x, y, z) (z-1)*nx*ny + (y-1)*nx + x;
[ind2x, ind2y, ind2z] = ndgrid(1:nx, 1:ny, 1:nz);
ind2x = ind2x(:); ind2y = ind2y(:); ind2z = ind2z(:);

% 获取邻居配置
[neighbor_offsets, neighbor_distances] = get_optimized_neighbors(connectivity);
num_neighbors = size(neighbor_offsets, 1);

% 使用MATLAB的优先队列实现 (基于Java)
import java.util.PriorityQueue;
import java.util.Comparator;

% 创建比较器
comparator = Comparator.comparingDouble(@(x) x(1));
pq = PriorityQueue(1000, comparator);

% 初始化源点
for s = 1:num_sources
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    if sx >= 1 && sx <= nx && sy >= 1 && sy <= ny && sz >= 1 && sz <= nz
        T(sx, sy, sz) = 0;
        linear_idx = sub2ind_fast(sx, sy, sz);
        pq.offer([0, linear_idx]);
    end
end

fprintf('开始优化Dijkstra算法，网格大小: %dx%dx%d，连接性: %d\n', nx, ny, nz, connectivity);
processed_count = 0;
total_nodes = nx * ny * nz;

% 预计算速度倒数以提高效率
inv_speed = 1 ./ max(SpeedImage, 1e-8);

% 主循环
tic;
while ~pq.isEmpty()
    % 提取最小元素
    current = pq.poll();
    current_dist = current(1);
    current_idx = current(2);
    
    % 转换为3D坐标
    cx = ind2x(current_idx);
    cy = ind2y(current_idx);
    cz = ind2z(current_idx);
    
    % 跳过已访问的节点
    if visited(cx, cy, cz)
        continue;
    end
    
    % 标记为已访问
    visited(cx, cy, cz) = true;
    processed_count = processed_count + 1;
    
    % 显示进度
    if mod(processed_count, 50000) == 0
        elapsed = toc;
        rate = processed_count / elapsed;
        remaining = (total_nodes - processed_count) / rate;
        fprintf('进度: %d/%d (%.1f%%), 速度: %.0f节点/秒, 预计剩余: %.1f秒\n', ...
            processed_count, total_nodes, 100*processed_count/total_nodes, rate, remaining);
    end
    
    % 检查所有邻居
    for n = 1:num_neighbors
        nx_coord = cx + neighbor_offsets(n, 1);
        ny_coord = cy + neighbor_offsets(n, 2);
        nz_coord = cz + neighbor_offsets(n, 3);
        
        % 边界检查
        if nx_coord < 1 || nx_coord > nx || ...
           ny_coord < 1 || ny_coord > ny || ...
           nz_coord < 1 || nz_coord > nz
            continue;
        end
        
        % 跳过已访问的邻居
        if visited(nx_coord, ny_coord, nz_coord)
            continue;
        end
        
        % 快速计算传播时间
        v_avg = 2 / (inv_speed(cx, cy, cz) + inv_speed(nx_coord, ny_coord, nz_coord));
        travel_time = neighbor_distances(n) / v_avg;
        new_distance = current_dist + travel_time;
        
        % 更新距离
        if new_distance < T(nx_coord, ny_coord, nz_coord)
            T(nx_coord, ny_coord, nz_coord) = new_distance;
            neighbor_idx = sub2ind_fast(nx_coord, ny_coord, nz_coord);
            pq.offer([new_distance, neighbor_idx]);
        end
    end
end

total_time = toc;
fprintf('Dijkstra算法完成！\n');
fprintf('总用时: %.2f秒, 处理节点: %d, 平均速度: %.0f节点/秒\n', ...
    total_time, processed_count, processed_count/total_time);

end

function [neighbor_offsets, neighbor_distances] = get_optimized_neighbors(connectivity)
%GET_OPTIMIZED_NEIGHBORS 获取优化的邻居偏移和距离

switch connectivity
    case 6  % 6-连通 (面邻居)
        neighbor_offsets = [
            -1,  0,  0;   1,  0,  0;   0, -1,  0;
             0,  1,  0;   0,  0, -1;   0,  0,  1
        ];
        neighbor_distances = ones(6, 1);  % 所有面邻居距离为1
        
    case 18  % 18-连通 (面+边邻居)
        neighbor_offsets = [
            % 面邻居 (距离 = 1)
            -1,  0,  0;   1,  0,  0;   0, -1,  0;   0,  1,  0;   0,  0, -1;   0,  0,  1;
            % 边邻居 (距离 = sqrt(2))
            -1, -1,  0;  -1,  1,  0;   1, -1,  0;   1,  1,  0;
            -1,  0, -1;  -1,  0,  1;   1,  0, -1;   1,  0,  1;
             0, -1, -1;   0, -1,  1;   0,  1, -1;   0,  1,  1
        ];
        neighbor_distances = [ones(6, 1); sqrt(2)*ones(12, 1)];
        
    case 26  % 26-连通 (面+边+角邻居)
        neighbor_offsets = [
            % 面邻居 (距离 = 1)
            -1,  0,  0;   1,  0,  0;   0, -1,  0;   0,  1,  0;   0,  0, -1;   0,  0,  1;
            % 边邻居 (距离 = sqrt(2))
            -1, -1,  0;  -1,  1,  0;   1, -1,  0;   1,  1,  0;
            -1,  0, -1;  -1,  0,  1;   1,  0, -1;   1,  0,  1;
             0, -1, -1;   0, -1,  1;   0,  1, -1;   0,  1,  1;
            % 角邻居 (距离 = sqrt(3))
            -1, -1, -1;  -1, -1,  1;  -1,  1, -1;  -1,  1,  1;
             1, -1, -1;   1, -1,  1;   1,  1, -1;   1,  1,  1
        ];
        neighbor_distances = [ones(6, 1); sqrt(2)*ones(12, 1); sqrt(3)*ones(8, 1)];
        
    otherwise
        error('连接性必须是 6, 18, 或 26');
end

end
