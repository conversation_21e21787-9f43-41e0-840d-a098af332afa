% 测试Runge-Kutta射线追踪法
% 此脚本用于验证RK射线追踪法能否替代MSFM2D

clear; clc;

fprintf('=== Runge-Kutta射线追踪法测试 ===\n\n');

%% 1. 创建简单的测试速度模型
fprintf('1. 创建测试速度模型...\n');

% 创建一个简单的分层速度模型
model_size = 100;
SpeedImage = zeros(model_size, model_size);

% 分层结构
SpeedImage(1:25, :) = 2000;   % 第一层
SpeedImage(26:50, :) = 3000;  % 第二层
SpeedImage(51:75, :) = 4000;  % 第三层
SpeedImage(76:100, :) = 5000; % 第四层

% 添加一个低速异常体
SpeedImage(40:60, 40:60) = 1500;

fprintf('   速度模型大小: %d × %d\n', size(SpeedImage, 1), size(SpeedImage, 2));
fprintf('   速度范围: %.0f - %.0f m/s\n', min(SpeedImage(:)), max(SpeedImage(:)));

%% 2. 设置震源和台站
fprintf('\n2. 设置震源和台站位置...\n');

source_pos = [20, 20];  % 震源位置
stations = [80, 20;     % 台站1
           80, 80;      % 台站2
           20, 80];     % 台站3

fprintf('   震源位置: [%d, %d]\n', source_pos(1), source_pos(2));
fprintf('   台站数量: %d\n', size(stations, 1));

%% 3. 使用RK射线追踪计算走时表
fprintf('\n3. 使用Runge-Kutta射线追踪计算走时表...\n');

tic;
try
    % 调用RK射线追踪函数
    T_rk = rk_raytracing2d(SpeedImage, source_pos');
    rk_time = toc;
    fprintf('   RK射线追踪计算完成，用时: %.2f秒\n', rk_time);
    rk_success = true;
catch ME
    fprintf('   RK射线追踪计算失败: %s\n', ME.message);
    rk_success = false;
    rk_time = toc;
end

%% 4. 如果有MSFM2D，进行对比测试
fprintf('\n4. 尝试与MSFM2D对比...\n');

if exist('msfm2d', 'file')
    try
        tic;
        T_msfm = msfm2d(SpeedImage, source_pos', true, true);
        msfm_time = toc;
        fprintf('   MSFM2D计算完成，用时: %.2f秒\n', msfm_time);
        msfm_success = true;
    catch ME
        fprintf('   MSFM2D计算失败: %s\n', ME.message);
        msfm_success = false;
    end
else
    fprintf('   MSFM2D函数不可用，跳过对比\n');
    msfm_success = false;
end

%% 5. 分析结果
fprintf('\n5. 结果分析...\n');

if rk_success
    % 计算各台站的走时
    fprintf('   各台站的走时 (RK射线追踪):\n');
    for i = 1:size(stations, 1)
        station_time = T_rk(stations(i, 1), stations(i, 2));
        fprintf('     台站%d [%d,%d]: %.4f秒\n', i, stations(i, 1), stations(i, 2), station_time);
    end
    
    % 检查走时表的合理性
    fprintf('\n   走时表统计:\n');
    fprintf('     最小走时: %.4f秒\n', min(T_rk(:)));
    fprintf('     最大走时: %.4f秒\n', max(T_rk(:)));
    fprintf('     平均走时: %.4f秒\n', mean(T_rk(:)));
    
    % 检查震源点走时
    source_time = T_rk(source_pos(1), source_pos(2));
    fprintf('     震源点走时: %.6f秒 (应该接近0)\n', source_time);
    
    if msfm_success
        % 对比两种方法的结果
        fprintf('\n   与MSFM2D对比:\n');
        diff_map = abs(T_rk - T_msfm);
        fprintf('     最大差异: %.4f秒\n', max(diff_map(:)));
        fprintf('     平均差异: %.4f秒\n', mean(diff_map(:)));
        fprintf('     RMS差异: %.4f秒\n', sqrt(mean(diff_map(:).^2)));
        
        % 计算相关系数
        valid_idx = isfinite(T_rk) & isfinite(T_msfm);
        if sum(valid_idx(:)) > 0
            correlation = corrcoef(T_rk(valid_idx), T_msfm(valid_idx));
            fprintf('     相关系数: %.4f\n', correlation(1,2));
        end
    end
end

%% 6. 可视化结果
fprintf('\n6. 生成可视化图形...\n');

if rk_success
    figure('Position', [100, 100, 1200, 800]);
    
    % 子图1: 速度模型
    subplot(2, 3, 1);
    pcolor(SpeedImage); shading interp; colorbar;
    hold on;
    plot(source_pos(2), source_pos(1), '*r', 'MarkerSize', 15, 'LineWidth', 2);
    plot(stations(:, 2), stations(:, 1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
    title('速度模型');
    xlabel('Y'); ylabel('X');
    
    % 子图2: RK走时表
    subplot(2, 3, 2);
    pcolor(T_rk); shading interp; colorbar;
    hold on;
    plot(source_pos(2), source_pos(1), '*r', 'MarkerSize', 15, 'LineWidth', 2);
    plot(stations(:, 2), stations(:, 1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
    title('RK射线追踪走时表');
    xlabel('Y'); ylabel('X');
    
    if msfm_success
        % 子图3: MSFM走时表
        subplot(2, 3, 3);
        pcolor(T_msfm); shading interp; colorbar;
        hold on;
        plot(source_pos(2), source_pos(1), '*r', 'MarkerSize', 15, 'LineWidth', 2);
        plot(stations(:, 2), stations(:, 1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
        title('MSFM2D走时表');
        xlabel('Y'); ylabel('X');
        
        % 子图4: 差异图
        subplot(2, 3, 4);
        pcolor(diff_map); shading interp; colorbar;
        title('走时差异 |RK - MSFM|');
        xlabel('Y'); ylabel('X');
        
        % 子图5: 散点对比
        subplot(2, 3, 5);
        valid_idx = isfinite(T_rk) & isfinite(T_msfm);
        scatter(T_msfm(valid_idx), T_rk(valid_idx), 10, 'filled', 'alpha', 0.6);
        hold on;
        plot([min(T_msfm(valid_idx)), max(T_msfm(valid_idx))], ...
             [min(T_msfm(valid_idx)), max(T_msfm(valid_idx))], 'r--', 'LineWidth', 2);
        xlabel('MSFM2D走时 (秒)');
        ylabel('RK射线追踪走时 (秒)');
        title('走时对比');
        grid on;
        
        % 子图6: 沿某条线的走时剖面
        subplot(2, 3, 6);
        line_idx = round(model_size/2);
        plot(1:model_size, T_rk(line_idx, :), 'b-', 'LineWidth', 2, 'DisplayName', 'RK射线追踪');
        hold on;
        plot(1:model_size, T_msfm(line_idx, :), 'r--', 'LineWidth', 2, 'DisplayName', 'MSFM2D');
        xlabel('Y坐标');
        ylabel('走时 (秒)');
        title(sprintf('第%d行走时剖面', line_idx));
        legend('Location', 'best');
        grid on;
    else
        % 如果没有MSFM对比，显示RK结果的等时线
        subplot(2, 3, [3, 6]);
        contour(T_rk, 20, 'ShowText', 'on');
        hold on;
        plot(source_pos(2), source_pos(1), '*r', 'MarkerSize', 15, 'LineWidth', 2);
        plot(stations(:, 2), stations(:, 1), '^k', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
        title('RK射线追踪等时线');
        xlabel('Y'); ylabel('X');
        colorbar;
    end
    
    sgtitle('Runge-Kutta射线追踪测试结果');
end

%% 7. 总结
fprintf('\n=== 测试总结 ===\n');
if rk_success
    fprintf('✓ Runge-Kutta射线追踪法实现成功\n');
    fprintf('✓ 计算用时: %.2f秒\n', rk_time);
    if msfm_success
        fprintf('✓ 与MSFM2D对比完成\n');
        if max(diff_map(:)) < 0.01
            fprintf('✓ 两种方法结果高度一致 (最大差异 < 0.01秒)\n');
        elseif max(diff_map(:)) < 0.1
            fprintf('⚠ 两种方法结果基本一致 (最大差异 < 0.1秒)\n');
        else
            fprintf('⚠ 两种方法存在较大差异，需要进一步调试\n');
        end
    end
    fprintf('✓ 可视化图形已生成\n');
else
    fprintf('✗ Runge-Kutta射线追踪法实现失败\n');
    fprintf('  请检查代码实现和编译环境\n');
end

fprintf('\n测试完成!\n');
