function T = bellman_ford_3d_optimized(SpeedImage, SourcePoints, options)
%BELLMAN_FORD_3D_OPTIMIZED 优化版Bellman-Ford算法3D实现
% 包含SPFA优化、早期终止和内存优化
%
% 输入:
%   SpeedImage - 3D速度场 [nx x ny x nz]
%   SourcePoints - 源点坐标 [3 x N] (1-based索引)
%   options - 选项结构体:
%     .connectivity - 连接性: 6, 18, 或 26 (默认6)
%     .algorithm - 算法类型: 'standard', 'spfa', 'queue_optimized' (默认'spfa')
%     .max_iterations - 最大迭代次数 (默认V-1)
%     .early_termination - 早期终止阈值 (默认true)
%     .negative_cycle_detection - 负环检测 (默认true)
%     .memory_efficient - 内存优化模式 (默认false)
%
% 输出:
%   T - 传播时间场 [nx x ny x nz]

% 默认选项
if nargin < 3, options = struct(); end
if ~isfield(options, 'connectivity'), options.connectivity = 6; end
if ~isfield(options, 'algorithm'), options.algorithm = 'spfa'; end
if ~isfield(options, 'max_iterations'), options.max_iterations = []; end
if ~isfield(options, 'early_termination'), options.early_termination = true; end
if ~isfield(options, 'negative_cycle_detection'), options.negative_cycle_detection = true; end
if ~isfield(options, 'memory_efficient'), options.memory_efficient = false; end

% 获取尺寸
[nx, ny, nz] = size(SpeedImage);
num_sources = size(SourcePoints, 2);
total_nodes = nx * ny * nz;

% 设置默认最大迭代次数
if isempty(options.max_iterations)
    options.max_iterations = total_nodes - 1;
end

fprintf('开始优化Bellman-Ford算法\n');
fprintf('配置: 算法=%s, 连接性=%d, 内存优化=%d\n', ...
    options.algorithm, options.connectivity, options.memory_efficient);

% 根据算法类型选择实现
switch options.algorithm
    case 'standard'
        T = execute_standard_bellman_ford(SpeedImage, SourcePoints, options);
    case 'spfa'
        T = execute_spfa_algorithm(SpeedImage, SourcePoints, options);
    case 'queue_optimized'
        T = execute_queue_optimized_bellman_ford(SpeedImage, SourcePoints, options);
    otherwise
        error('未知算法类型: %s', options.algorithm);
end

end

function T = execute_standard_bellman_ford(SpeedImage, SourcePoints, options)
%EXECUTE_STANDARD_BELLMAN_FORD 执行标准Bellman-Ford算法

fprintf('执行标准Bellman-Ford算法...\n');

[nx, ny, nz] = size(SpeedImage);
T = inf(nx, ny, nz);

% 初始化源点
for s = 1:size(SourcePoints, 2)
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    if sx >= 1 && sx <= nx && sy >= 1 && sy <= ny && sz >= 1 && sz <= nz
        T(sx, sy, sz) = 0;
    end
end

% 获取邻居配置
neighbors = get_neighbor_offsets(options.connectivity);

% 主循环
tic;
for iteration = 1:options.max_iterations
    updated = false;
    
    % 遍历所有节点
    for x = 1:nx
        for y = 1:ny
            for z = 1:nz
                if T(x, y, z) == inf, continue; end
                
                % 松弛所有邻居
                for n = 1:size(neighbors, 1)
                    nx_coord = x + neighbors(n, 1);
                    ny_coord = y + neighbors(n, 2);
                    nz_coord = z + neighbors(n, 3);
                    
                    if nx_coord >= 1 && nx_coord <= nx && ...
                       ny_coord >= 1 && ny_coord <= ny && ...
                       nz_coord >= 1 && nz_coord <= nz
                        
                        weight = calculate_travel_time_opt(SpeedImage, ...
                            x, y, z, nx_coord, ny_coord, nz_coord);
                        new_distance = T(x, y, z) + weight;
                        
                        if new_distance < T(nx_coord, ny_coord, nz_coord)
                            T(nx_coord, ny_coord, nz_coord) = new_distance;
                            updated = true;
                        end
                    end
                end
            end
        end
    end
    
    % 显示进度
    if mod(iteration, 50) == 0
        elapsed = toc;
        fprintf('标准BF进度: %d/%d 迭代, 用时: %.2f秒\n', ...
            iteration, options.max_iterations, elapsed);
    end
    
    % 早期终止
    if options.early_termination && ~updated
        fprintf('标准BF在第 %d 次迭代收敛\n', iteration);
        break;
    end
end

total_time = toc;
fprintf('标准Bellman-Ford完成，用时: %.2f秒\n', total_time);

end

function T = execute_spfa_algorithm(SpeedImage, SourcePoints, options)
%EXECUTE_SPFA_ALGORITHM 执行SPFA (Shortest Path Faster Algorithm)

fprintf('执行SPFA算法...\n');

[nx, ny, nz] = size(SpeedImage);
T = inf(nx, ny, nz);
in_queue = false(nx, ny, nz);
update_count = zeros(nx, ny, nz);

% 初始化队列
queue = [];

% 初始化源点
for s = 1:size(SourcePoints, 2)
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    if sx >= 1 && sx <= nx && sy >= 1 && sy <= ny && sz >= 1 && sz <= nz
        T(sx, sy, sz) = 0;
        queue = [queue; [sx, sy, sz]];
        in_queue(sx, sy, sz) = true;
    end
end

% 获取邻居配置
neighbors = get_neighbor_offsets(options.connectivity);
num_neighbors = size(neighbors, 1);

% SPFA主循环
tic;
iteration = 0;
max_queue_size = 0;

while ~isempty(queue) && iteration < options.max_iterations
    iteration = iteration + 1;
    max_queue_size = max(max_queue_size, size(queue, 1));
    
    % 从队列中取出节点
    current = queue(1, :);
    queue(1, :) = [];
    
    cx = current(1);
    cy = current(2);
    cz = current(3);
    
    in_queue(cx, cy, cz) = false;
    
    % 检测负环
    update_count(cx, cy, cz) = update_count(cx, cy, cz) + 1;
    if options.negative_cycle_detection && update_count(cx, cy, cz) > nx + ny + nz
        warning('SPFA检测到可能的负环，节点 (%d,%d,%d)', cx, cy, cz);
        break;
    end
    
    % 松弛所有邻居
    for n = 1:num_neighbors
        nx_coord = cx + neighbors(n, 1);
        ny_coord = cy + neighbors(n, 2);
        nz_coord = cz + neighbors(n, 3);
        
        if nx_coord >= 1 && nx_coord <= nx && ...
           ny_coord >= 1 && ny_coord <= ny && ...
           nz_coord >= 1 && nz_coord <= nz
            
            weight = calculate_travel_time_opt(SpeedImage, ...
                cx, cy, cz, nx_coord, ny_coord, nz_coord);
            new_distance = T(cx, cy, cz) + weight;
            
            if new_distance < T(nx_coord, ny_coord, nz_coord)
                T(nx_coord, ny_coord, nz_coord) = new_distance;
                
                % 如果邻居不在队列中，加入队列
                if ~in_queue(nx_coord, ny_coord, nz_coord)
                    queue = [queue; [nx_coord, ny_coord, nz_coord]];
                    in_queue(nx_coord, ny_coord, nz_coord) = true;
                end
            end
        end
    end
    
    % 显示进度
    if mod(iteration, 10000) == 0
        elapsed = toc;
        rate = iteration / elapsed;
        fprintf('SPFA进度: %d 次处理, 队列大小: %d, 速度: %.0f 处理/秒\n', ...
            iteration, size(queue, 1), rate);
    end
end

total_time = toc;
fprintf('SPFA算法完成！\n');
fprintf('总用时: %.2f秒, 处理次数: %d, 最大队列大小: %d\n', ...
    total_time, iteration, max_queue_size);

end

function T = execute_queue_optimized_bellman_ford(SpeedImage, SourcePoints, options)
%EXECUTE_QUEUE_OPTIMIZED_BELLMAN_FORD 执行队列优化的Bellman-Ford

fprintf('执行队列优化Bellman-Ford算法...\n');

[nx, ny, nz] = size(SpeedImage);
T = inf(nx, ny, nz);

% 使用双端队列优化
current_layer = [];
next_layer = [];

% 初始化源点
for s = 1:size(SourcePoints, 2)
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    if sx >= 1 && sx <= nx && sy >= 1 && sy <= ny && sz >= 1 && sz <= nz
        T(sx, sy, sz) = 0;
        current_layer = [current_layer; [sx, sy, sz]];
    end
end

% 获取邻居配置
neighbors = get_neighbor_offsets(options.connectivity);

% 主循环
tic;
iteration = 0;

while ~isempty(current_layer) && iteration < options.max_iterations
    iteration = iteration + 1;
    next_layer = [];
    
    % 处理当前层的所有节点
    for i = 1:size(current_layer, 1)
        cx = current_layer(i, 1);
        cy = current_layer(i, 2);
        cz = current_layer(i, 3);
        
        % 松弛所有邻居
        for n = 1:size(neighbors, 1)
            nx_coord = cx + neighbors(n, 1);
            ny_coord = cy + neighbors(n, 2);
            nz_coord = cz + neighbors(n, 3);
            
            if nx_coord >= 1 && nx_coord <= nx && ...
               ny_coord >= 1 && ny_coord <= ny && ...
               nz_coord >= 1 && nz_coord <= nz
                
                weight = calculate_travel_time_opt(SpeedImage, ...
                    cx, cy, cz, nx_coord, ny_coord, nz_coord);
                new_distance = T(cx, cy, cz) + weight;
                
                if new_distance < T(nx_coord, ny_coord, nz_coord)
                    T(nx_coord, ny_coord, nz_coord) = new_distance;
                    
                    % 添加到下一层（避免重复）
                    if ~any(all(next_layer == [nx_coord, ny_coord, nz_coord], 2))
                        next_layer = [next_layer; [nx_coord, ny_coord, nz_coord]];
                    end
                end
            end
        end
    end
    
    % 切换到下一层
    current_layer = next_layer;
    
    % 显示进度
    if mod(iteration, 10) == 0
        elapsed = toc;
        fprintf('队列优化BF: 第 %d 层, 节点数: %d, 用时: %.2f秒\n', ...
            iteration, size(current_layer, 1), elapsed);
    end
end

total_time = toc;
fprintf('队列优化Bellman-Ford完成，用时: %.2f秒，总层数: %d\n', total_time, iteration);

end

function neighbors = get_neighbor_offsets(connectivity)
%GET_NEIGHBOR_OFFSETS 获取邻居偏移量

switch connectivity
    case 6
        neighbors = [-1,0,0; 1,0,0; 0,-1,0; 0,1,0; 0,0,-1; 0,0,1];
        
    case 18
        neighbors = [
            -1,0,0; 1,0,0; 0,-1,0; 0,1,0; 0,0,-1; 0,0,1;
            -1,-1,0; -1,1,0; 1,-1,0; 1,1,0;
            -1,0,-1; -1,0,1; 1,0,-1; 1,0,1;
            0,-1,-1; 0,-1,1; 0,1,-1; 0,1,1
        ];
        
    case 26
        neighbors = [
            -1,0,0; 1,0,0; 0,-1,0; 0,1,0; 0,0,-1; 0,0,1;
            -1,-1,0; -1,1,0; 1,-1,0; 1,1,0;
            -1,0,-1; -1,0,1; 1,0,-1; 1,0,1;
            0,-1,-1; 0,-1,1; 0,1,-1; 0,1,1;
            -1,-1,-1; -1,-1,1; -1,1,-1; -1,1,1;
            1,-1,-1; 1,-1,1; 1,1,-1; 1,1,1
        ];
        
    otherwise
        error('连接性必须是 6, 18, 或 26');
end

end

function travel_time = calculate_travel_time_opt(SpeedImage, x1, y1, z1, x2, y2, z2)
%CALCULATE_TRAVEL_TIME_OPT 优化的传播时间计算

% 获取两点的速度
v1 = SpeedImage(x1, y1, z1);
v2 = SpeedImage(x2, y2, z2);

% 使用调和平均速度
v_avg = 2 / (1/max(v1, 1e-8) + 1/max(v2, 1e-8));

% 计算欧几里得距离
dx = x2 - x1;
dy = y2 - y1;
dz = z2 - z1;
distance = sqrt(dx^2 + dy^2 + dz^2);

% 返回传播时间
travel_time = distance / v_avg;

end
