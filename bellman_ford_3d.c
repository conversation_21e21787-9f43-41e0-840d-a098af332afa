#include "mex.h"
#include "math.h"
#include <stdlib.h>
#include <stdbool.h>

/*
 * Bellman-Ford Algorithm for 3D Distance Calculation
 * This function calculates travel times using Bellman-Ford algorithm
 * which can handle negative weights and detect negative cycles.
 * 
 * T = bellman_ford_3d(F, SourcePoints, connectivity, max_iterations)
 * 
 * Inputs:
 *   F: The 3D speed/velocity image
 *   SourcePoints: A list of starting points [3 x N]
 *   connectivity: 6 (face neighbors), 18 (face+edge), or 26 (face+edge+corner)
 *   max_iterations: Maximum number of iterations (default: V-1)
 * 
 * Outputs:
 *   T: Travel time image from SourcePoints to all pixels
 */

#define INF 1e10
#define EPS 1e-8

/* Edge structure for Bellman-Ford */
typedef struct {
    int from_x, from_y, from_z;
    int to_x, to_y, to_z;
    int from_index, to_index;
    double weight;
} Edge;

/* Helper function to calculate 3D index */
int mindex3(int x, int y, int z, int sizx, int sizy) {
    return z*sizx*sizy + y*sizx + x;
}

/* Check if coordinates are within bounds */
bool inbounds3d(int x, int y, int z, const mwSize *dims) {
    return (x >= 0 && x < dims[0] && y >= 0 && y < dims[1] && z >= 0 && z < dims[2]);
}

/* Calculate travel time between adjacent voxels */
double calculate_travel_time(double *F, const mwSize *dims, 
                           int x1, int y1, int z1, 
                           int x2, int y2, int z2) {
    double v1 = F[mindex3(x1, y1, z1, dims[0], dims[1])];
    double v2 = F[mindex3(x2, y2, z2, dims[0], dims[1])];
    
    /* Use harmonic mean of velocities */
    double v_avg = 2.0 / (1.0/fmax(v1, EPS) + 1.0/fmax(v2, EPS));
    
    /* Calculate Euclidean distance */
    double dx = x2 - x1;
    double dy = y2 - y1;
    double dz = z2 - z1;
    double distance = sqrt(dx*dx + dy*dy + dz*dz);
    
    return distance / v_avg;
}

/* Get neighbor offsets */
void get_neighbors(int connectivity, int **neighbors, int *num_neighbors) {
    static int neighbors_6[6][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1}
    };
    
    static int neighbors_18[18][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1},
        {-1, -1, 0}, {-1, 1, 0}, {1, -1, 0}, {1, 1, 0},
        {-1, 0, -1}, {-1, 0, 1}, {1, 0, -1}, {1, 0, 1},
        {0, -1, -1}, {0, -1, 1}, {0, 1, -1}, {0, 1, 1}
    };
    
    static int neighbors_26[26][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1},
        {-1, -1, 0}, {-1, 1, 0}, {1, -1, 0}, {1, 1, 0},
        {-1, 0, -1}, {-1, 0, 1}, {1, 0, -1}, {1, 0, 1},
        {0, -1, -1}, {0, -1, 1}, {0, 1, -1}, {0, 1, 1},
        {-1, -1, -1}, {-1, -1, 1}, {-1, 1, -1}, {-1, 1, 1},
        {1, -1, -1}, {1, -1, 1}, {1, 1, -1}, {1, 1, 1}
    };
    
    switch (connectivity) {
        case 6:
            *neighbors = (int*)neighbors_6;
            *num_neighbors = 6;
            break;
        case 18:
            *neighbors = (int*)neighbors_18;
            *num_neighbors = 18;
            break;
        case 26:
            *neighbors = (int*)neighbors_26;
            *num_neighbors = 26;
            break;
        default:
            *neighbors = (int*)neighbors_6;
            *num_neighbors = 6;
            break;
    }
}

/* Build edge list for Bellman-Ford algorithm */
int build_edge_list(double *F, const mwSize *dims, int connectivity, Edge **edges) {
    int *neighbors;
    int num_neighbors;
    get_neighbors(connectivity, &neighbors, &num_neighbors);
    
    /* Estimate maximum number of edges */
    int max_edges = dims[0] * dims[1] * dims[2] * num_neighbors;
    *edges = (Edge*)malloc(max_edges * sizeof(Edge));
    
    int edge_count = 0;
    
    /* Build edges for all valid neighbor pairs */
    for (int z = 0; z < dims[2]; z++) {
        for (int y = 0; y < dims[1]; y++) {
            for (int x = 0; x < dims[0]; x++) {
                int from_idx = mindex3(x, y, z, dims[0], dims[1]);
                
                for (int n = 0; n < num_neighbors; n++) {
                    int nx = x + neighbors[n*3 + 0];
                    int ny = y + neighbors[n*3 + 1];
                    int nz = z + neighbors[n*3 + 2];
                    
                    if (inbounds3d(nx, ny, nz, dims)) {
                        int to_idx = mindex3(nx, ny, nz, dims[0], dims[1]);
                        
                        /* Calculate edge weight (travel time) */
                        double weight = calculate_travel_time(F, dims, x, y, z, nx, ny, nz);
                        
                        /* Add edge */
                        (*edges)[edge_count].from_x = x;
                        (*edges)[edge_count].from_y = y;
                        (*edges)[edge_count].from_z = z;
                        (*edges)[edge_count].to_x = nx;
                        (*edges)[edge_count].to_y = ny;
                        (*edges)[edge_count].to_z = nz;
                        (*edges)[edge_count].from_index = from_idx;
                        (*edges)[edge_count].to_index = to_idx;
                        (*edges)[edge_count].weight = weight;
                        
                        edge_count++;
                    }
                }
            }
        }
    }
    
    return edge_count;
}

/* Main MEX function */
void mexFunction(int nlhs, mxArray *plhs[], int nrhs, const mxArray *prhs[]) {
    /* Input validation */
    if (nrhs < 2 || nrhs > 4) {
        mexErrMsgTxt("2 to 4 inputs required: F, SourcePoints, [connectivity], [max_iterations]");
    }
    if (nlhs != 1) {
        mexErrMsgTxt("One output required");
    }
    
    /* Check input types */
    if (mxGetClassID(prhs[0]) != mxDOUBLE_CLASS) {
        mexErrMsgTxt("Speed image must be of class double");
    }
    if (mxGetClassID(prhs[1]) != mxDOUBLE_CLASS) {
        mexErrMsgTxt("SourcePoints must be of class double");
    }
    
    /* Get input data */
    double *F = (double*)mxGetPr(prhs[0]);
    double *SourcePoints = (double*)mxGetPr(prhs[1]);
    
    /* Get optional parameters */
    int connectivity = (nrhs > 2) ? (int)mxGetScalar(prhs[2]) : 6;
    int max_iterations = (nrhs > 3) ? (int)mxGetScalar(prhs[3]) : 0;
    
    /* Get dimensions */
    const mwSize *dims = mxGetDimensions(prhs[0]);
    if (mxGetNumberOfDimensions(prhs[0]) != 3) {
        mexErrMsgTxt("Speed image must be 3D");
    }
    
    const mwSize *sp_dims = mxGetDimensions(prhs[1]);
    if (sp_dims[0] != 3) {
        mexErrMsgTxt("SourcePoints must be a 3xN matrix");
    }
    
    int npixels = dims[0] * dims[1] * dims[2];
    int num_sources = sp_dims[1];
    
    /* Set default max_iterations if not specified */
    if (max_iterations == 0) {
        max_iterations = npixels - 1;
    }
    
    /* Create output array */
    plhs[0] = mxCreateNumericArray(3, dims, mxDOUBLE_CLASS, mxREAL);
    double *T = mxGetPr(plhs[0]);
    
    /* Initialize distances */
    for (int i = 0; i < npixels; i++) {
        T[i] = INF;
    }
    
    /* Initialize source points */
    for (int s = 0; s < num_sources; s++) {
        int sx = (int)(SourcePoints[0 + s*3] - 1);  /* Convert to 0-based */
        int sy = (int)(SourcePoints[1 + s*3] - 1);
        int sz = (int)(SourcePoints[2 + s*3] - 1);
        
        if (inbounds3d(sx, sy, sz, dims)) {
            int idx = mindex3(sx, sy, sz, dims[0], dims[1]);
            T[idx] = 0.0;
        }
    }
    
    /* Build edge list */
    Edge *edges;
    int num_edges = build_edge_list(F, dims, connectivity, &edges);
    
    /* Bellman-Ford algorithm main loop */
    bool updated;
    int iteration = 0;
    
    for (iteration = 0; iteration < max_iterations; iteration++) {
        updated = false;
        
        /* Relax all edges */
        for (int e = 0; e < num_edges; e++) {
            int from_idx = edges[e].from_index;
            int to_idx = edges[e].to_index;
            double weight = edges[e].weight;
            
            if (T[from_idx] != INF && T[from_idx] + weight < T[to_idx]) {
                T[to_idx] = T[from_idx] + weight;
                updated = true;
            }
        }
        
        /* Early termination if no updates */
        if (!updated) {
            break;
        }
    }
    
    /* Check for negative cycles (optional) */
    bool has_negative_cycle = false;
    if (updated) {
        for (int e = 0; e < num_edges; e++) {
            int from_idx = edges[e].from_index;
            int to_idx = edges[e].to_index;
            double weight = edges[e].weight;
            
            if (T[from_idx] != INF && T[from_idx] + weight < T[to_idx]) {
                has_negative_cycle = true;
                break;
            }
        }
    }
    
    if (has_negative_cycle) {
        mexWarnMsgTxt("Negative cycle detected in the graph");
    }
    
    /* Cleanup */
    free(edges);
}
