function T = rk_raytracing2d(SpeedImage, SourcePoints, varargin)
% RK_RAYTRACING2D - 使用Runge-Kutta射线追踪法计算走时表
%
% 语法:
%   T = rk_raytracing2d(SpeedImage, SourcePoints)
%   T = rk_raytracing2d(SpeedImage, SourcePoints, 'UseSecond', true)
%   T = rk_raytracing2d(SpeedImage, SourcePoints, 'UseCross', true)
%
% 输入参数:
%   SpeedImage    - 速度模型 (2D矩阵)
%   SourcePoints  - 震源位置 [2×N] (MATLAB索引，从1开始)
%   UseSecond     - 兼容性参数，在射线追踪中不使用
%   UseCross      - 兼容性参数，在射线追踪中不使用
%
% 输出参数:
%   T - 走时表，与SpeedImage同样大小
%
% 示例:
%   SpeedImage = ones(100, 100) * 3000; % 均匀速度模型
%   SourcePoints = [50; 50];            % 震源在(50,50)
%   T = rk_raytracing2d(SpeedImage, SourcePoints);
%
% 注意:
%   - 此函数是msfm2d的射线追踪替代版本
%   - 使用四阶Runge-Kutta方法求解射线方程
%   - 支持复杂的非均匀速度模型
%
% 作者: [您的姓名]
% 日期: [当前日期]

% 检查输入参数
if nargin < 2
    error('至少需要2个输入参数：SpeedImage和SourcePoints');
end

% 检查SpeedImage
if ~isnumeric(SpeedImage) || ~ismatrix(SpeedImage)
    error('SpeedImage必须是数值矩阵');
end

% 检查SourcePoints
if ~isnumeric(SourcePoints) || size(SourcePoints, 1) ~= 2
    error('SourcePoints必须是2×N矩阵');
end

% 解析可选参数（为了兼容msfm2d接口）
p = inputParser;
addParameter(p, 'UseSecond', true, @islogical);
addParameter(p, 'UseCross', true, @islogical);
parse(p, varargin{:});

% 获取参数（虽然在射线追踪中不直接使用）
UseSecond = p.Results.UseSecond;
UseCross = p.Results.UseCross;

% 显示算法信息
fprintf('使用Runge-Kutta射线追踪法计算走时表...\n');
fprintf('速度模型大小: %d × %d\n', size(SpeedImage, 1), size(SpeedImage, 2));
fprintf('震源数量: %d\n', size(SourcePoints, 2));

% 检查是否已编译MEX文件
mex_file = 'rk_raytracing_mex';
if ~exist([mex_file '.' mexext], 'file')
    fprintf('MEX文件不存在，尝试编译...\n');
    try
        % 编译MEX文件
        mex_command = sprintf('mex %s.c -output %s', 'rk_raytracing', mex_file);
        eval(mex_command);
        fprintf('MEX文件编译成功!\n');
    catch ME
        warning('MEX编译失败，使用MATLAB实现: %s', ME.message);
        % 如果MEX编译失败，使用MATLAB实现
        T = rk_raytracing_matlab(SpeedImage, SourcePoints);
        return;
    end
end

% 调用MEX函数
try
    T = feval(mex_file, SpeedImage, SourcePoints);
catch ME
    warning('MEX函数调用失败，使用MATLAB实现: %s', ME.message);
    % 如果MEX调用失败，使用MATLAB实现
    T = rk_raytracing_matlab(SpeedImage, SourcePoints);
end

end

function T = rk_raytracing_matlab(SpeedImage, SourcePoints)
% MATLAB实现的射线追踪（备用方案）

fprintf('使用MATLAB实现的射线追踪...\n');

[rows, cols] = size(SpeedImage);
T = inf(rows, cols);

% 只处理第一个震源
if size(SourcePoints, 2) > 1
    warning('MATLAB版本只处理第一个震源');
end

source_x = SourcePoints(1, 1);
source_y = SourcePoints(2, 1);

% 设置震源点走时为0
T(source_x, source_y) = 0;

% 射线追踪参数
max_steps = 5000;
step_size = 0.5;
tolerance = 1e-6;

% 对每个网格点进行射线追踪
total_points = rows * cols;
completed = 0;

fprintf('开始计算走时表...\n');
tic;

for i = 1:rows
    for j = 1:cols
        if i == source_x && j == source_y
            continue; % 跳过震源点
        end
        
        % 射线追踪计算走时
        travel_time = trace_ray_matlab(SpeedImage, source_x, source_y, i, j, ...
                                     max_steps, step_size, tolerance);
        T(i, j) = travel_time;
        
        completed = completed + 1;
        if mod(completed, 1000) == 0
            elapsed = toc;
            progress = completed / total_points;
            estimated_total = elapsed / progress;
            remaining = estimated_total - elapsed;
            fprintf('进度: %.1f%% (剩余时间: %.1f秒)\n', progress * 100, remaining);
        end
    end
end

elapsed = toc;
fprintf('计算完成! 总用时: %.2f秒\n', elapsed);

end

function travel_time = trace_ray_matlab(SpeedImage, start_x, start_y, end_x, end_y, ...
                                       max_steps, step_size, tolerance)
% MATLAB版本的射线追踪函数

[rows, cols] = size(SpeedImage);

% 计算初始方向
dx = end_x - start_x;
dy = end_y - start_y;
dist = sqrt(dx^2 + dy^2);

if dist < tolerance
    travel_time = 0;
    return;
end

% 初始化射线状态
ray.x = start_x;
ray.y = start_y;
ray.t = 0;

% 获取起始点速度
v0 = interpolate_velocity_matlab(SpeedImage, start_x, start_y);

% 初始慢度方向
angle = atan2(dy, dx);
ray.px = cos(angle) / v0;
ray.py = sin(angle) / v0;

min_dist = inf;
best_time = inf;

% 射线追踪主循环
for step = 1:max_steps
    % 检查是否到达目标
    current_dx = ray.x - end_x;
    current_dy = ray.y - end_y;
    current_dist = sqrt(current_dx^2 + current_dy^2);
    
    if current_dist < min_dist
        min_dist = current_dist;
        best_time = ray.t;
    end
    
    if current_dist < tolerance
        travel_time = ray.t;
        return;
    end
    
    % 检查边界
    if ray.x < 1 || ray.x > rows || ray.y < 1 || ray.y > cols
        break;
    end
    
    % 如果距离开始增大太多，停止追踪
    if step > 100 && current_dist > 4 * min_dist
        break;
    end
    
    % Runge-Kutta积分步
    ray = runge_kutta_step_matlab(SpeedImage, ray, step_size);
end

travel_time = best_time;

end

function v = interpolate_velocity_matlab(SpeedImage, x, y)
% MATLAB版本的速度插值函数

[rows, cols] = size(SpeedImage);

% 边界检查
if x < 1 || x > rows || y < 1 || y > cols
    v = 1500; % 默认速度
    return;
end

% 双线性插值
i1 = floor(x);
j1 = floor(y);
i2 = min(i1 + 1, rows);
j2 = min(j1 + 1, cols);

fx = x - i1;
fy = y - j1;

v11 = SpeedImage(i1, j1);
v12 = SpeedImage(i1, j2);
v21 = SpeedImage(i2, j1);
v22 = SpeedImage(i2, j2);

v = v11 * (1-fx) * (1-fy) + v21 * fx * (1-fy) + ...
    v12 * (1-fx) * fy + v22 * fx * fy;

end

function ray_new = runge_kutta_step_matlab(SpeedImage, ray, dt)
% MATLAB版本的Runge-Kutta积分步

% k1
k1 = compute_derivatives_matlab(SpeedImage, ray);

% k2
temp_ray.x = ray.x + dt * k1.dx / 2;
temp_ray.y = ray.y + dt * k1.dy / 2;
temp_ray.px = ray.px + dt * k1.dpx / 2;
temp_ray.py = ray.py + dt * k1.dpy / 2;
temp_ray.t = ray.t + dt * k1.dt / 2;
k2 = compute_derivatives_matlab(SpeedImage, temp_ray);

% k3
temp_ray.x = ray.x + dt * k2.dx / 2;
temp_ray.y = ray.y + dt * k2.dy / 2;
temp_ray.px = ray.px + dt * k2.dpx / 2;
temp_ray.py = ray.py + dt * k2.dpy / 2;
temp_ray.t = ray.t + dt * k2.dt / 2;
k3 = compute_derivatives_matlab(SpeedImage, temp_ray);

% k4
temp_ray.x = ray.x + dt * k3.dx;
temp_ray.y = ray.y + dt * k3.dy;
temp_ray.px = ray.px + dt * k3.dpx;
temp_ray.py = ray.py + dt * k3.dpy;
temp_ray.t = ray.t + dt * k3.dt;
k4 = compute_derivatives_matlab(SpeedImage, temp_ray);

% 更新射线状态
ray_new.x = ray.x + dt * (k1.dx + 2*k2.dx + 2*k3.dx + k4.dx) / 6;
ray_new.y = ray.y + dt * (k1.dy + 2*k2.dy + 2*k3.dy + k4.dy) / 6;
ray_new.px = ray.px + dt * (k1.dpx + 2*k2.dpx + 2*k3.dpx + k4.dpx) / 6;
ray_new.py = ray.py + dt * (k1.dpy + 2*k2.dpy + 2*k3.dpy + k4.dpy) / 6;
ray_new.t = ray.t + dt * (k1.dt + 2*k2.dt + 2*k3.dt + k4.dt) / 6;

end

function deriv = compute_derivatives_matlab(SpeedImage, ray)
% 计算射线方程的导数

v = interpolate_velocity_matlab(SpeedImage, ray.x, ray.y);

% 计算速度梯度（有限差分）
h = 1.0;
v_xp = interpolate_velocity_matlab(SpeedImage, ray.x + h, ray.y);
v_xm = interpolate_velocity_matlab(SpeedImage, ray.x - h, ray.y);
v_yp = interpolate_velocity_matlab(SpeedImage, ray.x, ray.y + h);
v_ym = interpolate_velocity_matlab(SpeedImage, ray.x, ray.y - h);

grad_x = (v_xp - v_xm) / (2 * h);
grad_y = (v_yp - v_ym) / (2 * h);

% 射线方程
v3 = v^3;
deriv.dx = v^2 * ray.px;
deriv.dy = v^2 * ray.py;
deriv.dpx = -grad_x / v3;
deriv.dpy = -grad_y / v3;
deriv.dt = 1 / v;

end
