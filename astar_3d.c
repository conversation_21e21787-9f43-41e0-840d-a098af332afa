#include "mex.h"
#include "math.h"
#include <stdlib.h>
#include <stdbool.h>

/*
 * A* Algorithm for 3D Distance Calculation
 * This function calculates travel times using A* search algorithm
 * with heuristic guidance for improved efficiency over <PERSON>jkstra.
 * 
 * T = astar_3d(F, SourcePoints, connectivity, heuristic_weight)
 * 
 * Inputs:
 *   F: The 3D speed/velocity image
 *   SourcePoints: A list of starting points [3 x N]
 *   connectivity: 6 (face neighbors), 18 (face+edge), or 26 (face+edge+corner)
 *   heuristic_weight: Weight for heuristic function (default: 1.0)
 * 
 * Outputs:
 *   T: Travel time image from SourcePoints to all pixels
 */

#define INF 1e10
#define EPS 1e-8

/* A* node structure */
typedef struct {
    int x, y, z;
    double g_cost;      /* Actual cost from start */
    double h_cost;      /* Heuristic cost to goal */
    double f_cost;      /* Total cost (g + h) */
    int index;
    bool in_open_set;
    bool in_closed_set;
} AStarNode;

/* Priority queue for A* */
typedef struct {
    AStarNode **nodes;
    int size;
    int capacity;
} AStarQueue;

/* Helper function to calculate 3D index */
int mindex3(int x, int y, int z, int sizx, int sizy) {
    return z*sizx*sizy + y*sizx + x;
}

/* Check if coordinates are within bounds */
bool inbounds3d(int x, int y, int z, const mwSize *dims) {
    return (x >= 0 && x < dims[0] && y >= 0 && y < dims[1] && z >= 0 && z < dims[2]);
}

/* Calculate heuristic distance (Euclidean distance with average speed) */
double calculate_heuristic(int x1, int y1, int z1, int x2, int y2, int z2, 
                          double avg_speed, double weight) {
    double dx = x2 - x1;
    double dy = y2 - y1;
    double dz = z2 - z1;
    double distance = sqrt(dx*dx + dy*dy + dz*dz);
    return weight * distance / avg_speed;
}

/* Calculate travel time between adjacent voxels */
double calculate_travel_time(double *F, const mwSize *dims, 
                           int x1, int y1, int z1, 
                           int x2, int y2, int z2) {
    double v1 = F[mindex3(x1, y1, z1, dims[0], dims[1])];
    double v2 = F[mindex3(x2, y2, z2, dims[0], dims[1])];
    
    /* Use harmonic mean of velocities */
    double v_avg = 2.0 / (1.0/fmax(v1, EPS) + 1.0/fmax(v2, EPS));
    
    /* Calculate Euclidean distance */
    double dx = x2 - x1;
    double dy = y2 - y1;
    double dz = z2 - z1;
    double distance = sqrt(dx*dx + dy*dy + dz*dz);
    
    return distance / v_avg;
}

/* Create A* queue */
AStarQueue* astar_queue_create(int capacity) {
    AStarQueue *queue = (AStarQueue*)malloc(sizeof(AStarQueue));
    queue->nodes = (AStarNode**)malloc(capacity * sizeof(AStarNode*));
    queue->size = 0;
    queue->capacity = capacity;
    return queue;
}

/* Destroy A* queue */
void astar_queue_destroy(AStarQueue *queue) {
    free(queue->nodes);
    free(queue);
}

/* Swap nodes in queue */
void astar_swap(AStarQueue *queue, int i, int j) {
    AStarNode *temp = queue->nodes[i];
    queue->nodes[i] = queue->nodes[j];
    queue->nodes[j] = temp;
}

/* Heapify up based on f_cost */
void astar_heapify_up(AStarQueue *queue, int index) {
    while (index > 0) {
        int parent = (index - 1) / 2;
        if (queue->nodes[index]->f_cost >= queue->nodes[parent]->f_cost) break;
        astar_swap(queue, index, parent);
        index = parent;
    }
}

/* Heapify down based on f_cost */
void astar_heapify_down(AStarQueue *queue, int index) {
    while (true) {
        int smallest = index;
        int left = 2 * index + 1;
        int right = 2 * index + 2;
        
        if (left < queue->size && 
            queue->nodes[left]->f_cost < queue->nodes[smallest]->f_cost)
            smallest = left;
        if (right < queue->size && 
            queue->nodes[right]->f_cost < queue->nodes[smallest]->f_cost)
            smallest = right;
            
        if (smallest == index) break;
        astar_swap(queue, index, smallest);
        index = smallest;
    }
}

/* Insert node into A* queue */
void astar_insert(AStarQueue *queue, AStarNode *node) {
    if (queue->size >= queue->capacity) {
        queue->capacity *= 2;
        queue->nodes = (AStarNode**)realloc(queue->nodes, 
                                           queue->capacity * sizeof(AStarNode*));
    }
    
    queue->nodes[queue->size] = node;
    astar_heapify_up(queue, queue->size);
    queue->size++;
}

/* Extract minimum f_cost node */
AStarNode* astar_extract_min(AStarQueue *queue) {
    if (queue->size == 0) return NULL;
    
    AStarNode *min_node = queue->nodes[0];
    queue->nodes[0] = queue->nodes[queue->size - 1];
    queue->size--;
    if (queue->size > 0) {
        astar_heapify_down(queue, 0);
    }
    return min_node;
}

/* Get neighbor offsets */
void get_neighbors(int connectivity, int **neighbors, int *num_neighbors) {
    static int neighbors_6[6][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1}
    };
    
    static int neighbors_18[18][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1},
        {-1, -1, 0}, {-1, 1, 0}, {1, -1, 0}, {1, 1, 0},
        {-1, 0, -1}, {-1, 0, 1}, {1, 0, -1}, {1, 0, 1},
        {0, -1, -1}, {0, -1, 1}, {0, 1, -1}, {0, 1, 1}
    };
    
    static int neighbors_26[26][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1},
        {-1, -1, 0}, {-1, 1, 0}, {1, -1, 0}, {1, 1, 0},
        {-1, 0, -1}, {-1, 0, 1}, {1, 0, -1}, {1, 0, 1},
        {0, -1, -1}, {0, -1, 1}, {0, 1, -1}, {0, 1, 1},
        {-1, -1, -1}, {-1, -1, 1}, {-1, 1, -1}, {-1, 1, 1},
        {1, -1, -1}, {1, -1, 1}, {1, 1, -1}, {1, 1, 1}
    };
    
    switch (connectivity) {
        case 6:
            *neighbors = (int*)neighbors_6;
            *num_neighbors = 6;
            break;
        case 18:
            *neighbors = (int*)neighbors_18;
            *num_neighbors = 18;
            break;
        case 26:
            *neighbors = (int*)neighbors_26;
            *num_neighbors = 26;
            break;
        default:
            *neighbors = (int*)neighbors_6;
            *num_neighbors = 6;
            break;
    }
}

/* Calculate average speed for heuristic */
double calculate_average_speed(double *F, int npixels) {
    double sum = 0.0;
    for (int i = 0; i < npixels; i++) {
        sum += F[i];
    }
    return sum / npixels;
}

/* Main MEX function */
void mexFunction(int nlhs, mxArray *plhs[], int nrhs, const mxArray *prhs[]) {
    /* Input validation */
    if (nrhs < 2 || nrhs > 4) {
        mexErrMsgTxt("2 to 4 inputs required: F, SourcePoints, [connectivity], [heuristic_weight]");
    }
    if (nlhs != 1) {
        mexErrMsgTxt("One output required");
    }
    
    /* Get input data */
    double *F = (double*)mxGetPr(prhs[0]);
    double *SourcePoints = (double*)mxGetPr(prhs[1]);
    
    int connectivity = (nrhs > 2) ? (int)mxGetScalar(prhs[2]) : 6;
    double heuristic_weight = (nrhs > 3) ? mxGetScalar(prhs[3]) : 1.0;
    
    /* Get dimensions */
    const mwSize *dims = mxGetDimensions(prhs[0]);
    if (mxGetNumberOfDimensions(prhs[0]) != 3) {
        mexErrMsgTxt("Speed image must be 3D");
    }
    
    const mwSize *sp_dims = mxGetDimensions(prhs[1]);
    if (sp_dims[0] != 3) {
        mexErrMsgTxt("SourcePoints must be a 3xN matrix");
    }
    
    int npixels = dims[0] * dims[1] * dims[2];
    int num_sources = sp_dims[1];
    
    /* Create output array */
    plhs[0] = mxCreateNumericArray(3, dims, mxDOUBLE_CLASS, mxREAL);
    double *T = mxGetPr(plhs[0]);
    
    /* Initialize node array */
    AStarNode *nodes = (AStarNode*)calloc(npixels, sizeof(AStarNode));
    
    /* Initialize all nodes */
    for (int i = 0; i < npixels; i++) {
        nodes[i].g_cost = INF;
        nodes[i].h_cost = 0.0;
        nodes[i].f_cost = INF;
        nodes[i].in_open_set = false;
        nodes[i].in_closed_set = false;
        T[i] = INF;
    }
    
    /* Calculate average speed for heuristic */
    double avg_speed = calculate_average_speed(F, npixels);
    
    /* Create open set (priority queue) */
    AStarQueue *open_set = astar_queue_create(npixels);
    
    /* Initialize source points */
    for (int s = 0; s < num_sources; s++) {
        int sx = (int)(SourcePoints[0 + s*3] - 1);
        int sy = (int)(SourcePoints[1 + s*3] - 1);
        int sz = (int)(SourcePoints[2 + s*3] - 1);
        
        if (inbounds3d(sx, sy, sz, dims)) {
            int idx = mindex3(sx, sy, sz, dims[0], dims[1]);
            nodes[idx].x = sx;
            nodes[idx].y = sy;
            nodes[idx].z = sz;
            nodes[idx].g_cost = 0.0;
            nodes[idx].f_cost = 0.0;
            nodes[idx].index = idx;
            nodes[idx].in_open_set = true;
            T[idx] = 0.0;
            astar_insert(open_set, &nodes[idx]);
        }
    }
    
    /* Get neighbor configuration */
    int *neighbors;
    int num_neighbors;
    get_neighbors(connectivity, &neighbors, &num_neighbors);
    
    /* A* main loop */
    while (open_set->size > 0) {
        AStarNode *current = astar_extract_min(open_set);
        current->in_open_set = false;
        current->in_closed_set = true;
        
        /* Check all neighbors */
        for (int n = 0; n < num_neighbors; n++) {
            int nx = current->x + neighbors[n*3 + 0];
            int ny = current->y + neighbors[n*3 + 1];
            int nz = current->z + neighbors[n*3 + 2];
            
            if (!inbounds3d(nx, ny, nz, dims)) continue;
            
            int neighbor_idx = mindex3(nx, ny, nz, dims[0], dims[1]);
            AStarNode *neighbor = &nodes[neighbor_idx];
            
            if (neighbor->in_closed_set) continue;
            
            /* Calculate tentative g_cost */
            double travel_time = calculate_travel_time(F, dims, 
                                                     current->x, current->y, current->z,
                                                     nx, ny, nz);
            double tentative_g = current->g_cost + travel_time;
            
            /* Initialize neighbor if not done */
            if (neighbor->g_cost == INF) {
                neighbor->x = nx;
                neighbor->y = ny;
                neighbor->z = nz;
                neighbor->index = neighbor_idx;
            }
            
            /* Update if better path found */
            if (tentative_g < neighbor->g_cost) {
                neighbor->g_cost = tentative_g;
                
                /* Calculate heuristic for all source points and take minimum */
                double min_h = INF;
                for (int s = 0; s < num_sources; s++) {
                    int sx = (int)(SourcePoints[0 + s*3] - 1);
                    int sy = (int)(SourcePoints[1 + s*3] - 1);
                    int sz = (int)(SourcePoints[2 + s*3] - 1);
                    double h = calculate_heuristic(nx, ny, nz, sx, sy, sz, 
                                                 avg_speed, heuristic_weight);
                    if (h < min_h) min_h = h;
                }
                neighbor->h_cost = min_h;
                neighbor->f_cost = neighbor->g_cost + neighbor->h_cost;
                
                T[neighbor_idx] = neighbor->g_cost;
                
                if (!neighbor->in_open_set) {
                    neighbor->in_open_set = true;
                    astar_insert(open_set, neighbor);
                }
            }
        }
    }
    
    /* Cleanup */
    astar_queue_destroy(open_set);
    free(nodes);
}
