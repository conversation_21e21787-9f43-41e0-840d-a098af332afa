#include "mex.h"
#include "math.h"
#include <stdlib.h>
#include <stdbool.h>

/*
 * <PERSON><PERSON>'s Algorithm for 3D Distance Calculation
 * This function calculates travel times using <PERSON><PERSON>'s algorithm,
 * an optimized version of <PERSON><PERSON><PERSON> for integer or quantized weights.
 * 
 * T = dial_3d(F, SourcePoints, connectivity, scale_factor)
 * 
 * Inputs:
 *   F: The 3D speed/velocity image
 *   SourcePoints: A list of starting points [3 x N]
 *   connectivity: 6 (face neighbors), 18 (face+edge), or 26 (face+edge+corner)
 *   scale_factor: Scaling factor for quantization (default: 1000)
 * 
 * Outputs:
 *   T: Travel time image from SourcePoints to all pixels
 */

#define INF 1e10
#define EPS 1e-8
#define MAX_BUCKETS 1000000

/* Node structure for <PERSON><PERSON>'s algorithm */
typedef struct DialNode {
    int x, y, z;
    int index;
    struct DialNode *next;
} DialNode;

/* Bucket structure for <PERSON><PERSON>'s algorithm */
typedef struct {
    DialNode **buckets;
    int num_buckets;
    int current_bucket;
    int max_bucket_used;
} DialBuckets;

/* Helper function to calculate 3D index */
int mindex3(int x, int y, int z, int sizx, int sizy) {
    return z*sizx*sizy + y*sizx + x;
}

/* Check if coordinates are within bounds */
bool inbounds3d(int x, int y, int z, const mwSize *dims) {
    return (x >= 0 && x < dims[0] && y >= 0 && y < dims[1] && z >= 0 && z < dims[2]);
}

/* Create bucket structure */
DialBuckets* dial_create_buckets(int num_buckets) {
    DialBuckets *buckets = (DialBuckets*)malloc(sizeof(DialBuckets));
    buckets->buckets = (DialNode**)calloc(num_buckets, sizeof(DialNode*));
    buckets->num_buckets = num_buckets;
    buckets->current_bucket = 0;
    buckets->max_bucket_used = 0;
    return buckets;
}

/* Destroy bucket structure */
void dial_destroy_buckets(DialBuckets *buckets) {
    /* Free all nodes in buckets */
    for (int i = 0; i < buckets->num_buckets; i++) {
        DialNode *current = buckets->buckets[i];
        while (current != NULL) {
            DialNode *next = current->next;
            free(current);
            current = next;
        }
    }
    free(buckets->buckets);
    free(buckets);
}

/* Insert node into bucket */
void dial_insert(DialBuckets *buckets, int bucket_idx, int x, int y, int z, int index) {
    if (bucket_idx >= buckets->num_buckets) {
        /* Bucket overflow - use last bucket */
        bucket_idx = buckets->num_buckets - 1;
    }
    
    DialNode *new_node = (DialNode*)malloc(sizeof(DialNode));
    new_node->x = x;
    new_node->y = y;
    new_node->z = z;
    new_node->index = index;
    new_node->next = buckets->buckets[bucket_idx];
    buckets->buckets[bucket_idx] = new_node;
    
    if (bucket_idx > buckets->max_bucket_used) {
        buckets->max_bucket_used = bucket_idx;
    }
}

/* Extract minimum node from buckets */
DialNode* dial_extract_min(DialBuckets *buckets) {
    /* Find next non-empty bucket */
    while (buckets->current_bucket <= buckets->max_bucket_used) {
        if (buckets->buckets[buckets->current_bucket] != NULL) {
            DialNode *node = buckets->buckets[buckets->current_bucket];
            buckets->buckets[buckets->current_bucket] = node->next;
            return node;
        }
        buckets->current_bucket++;
    }
    return NULL;  /* No more nodes */
}

/* Calculate quantized travel time between adjacent voxels */
int calculate_quantized_travel_time(double *F, const mwSize *dims, 
                                  int x1, int y1, int z1, 
                                  int x2, int y2, int z2, 
                                  double scale_factor) {
    double v1 = F[mindex3(x1, y1, z1, dims[0], dims[1])];
    double v2 = F[mindex3(x2, y2, z2, dims[0], dims[1])];
    
    /* Use harmonic mean of velocities */
    double v_avg = 2.0 / (1.0/fmax(v1, EPS) + 1.0/fmax(v2, EPS));
    
    /* Calculate Euclidean distance */
    double dx = x2 - x1;
    double dy = y2 - y1;
    double dz = z2 - z1;
    double distance = sqrt(dx*dx + dy*dy + dz*dz);
    
    /* Calculate travel time and quantize */
    double travel_time = distance / v_avg;
    int quantized_time = (int)(travel_time * scale_factor + 0.5);
    
    return quantized_time;
}

/* Get neighbor offsets */
void get_neighbors(int connectivity, int **neighbors, int *num_neighbors) {
    static int neighbors_6[6][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1}
    };
    
    static int neighbors_18[18][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1},
        {-1, -1, 0}, {-1, 1, 0}, {1, -1, 0}, {1, 1, 0},
        {-1, 0, -1}, {-1, 0, 1}, {1, 0, -1}, {1, 0, 1},
        {0, -1, -1}, {0, -1, 1}, {0, 1, -1}, {0, 1, 1}
    };
    
    static int neighbors_26[26][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1},
        {-1, -1, 0}, {-1, 1, 0}, {1, -1, 0}, {1, 1, 0},
        {-1, 0, -1}, {-1, 0, 1}, {1, 0, -1}, {1, 0, 1},
        {0, -1, -1}, {0, -1, 1}, {0, 1, -1}, {0, 1, 1},
        {-1, -1, -1}, {-1, -1, 1}, {-1, 1, -1}, {-1, 1, 1},
        {1, -1, -1}, {1, -1, 1}, {1, 1, -1}, {1, 1, 1}
    };
    
    switch (connectivity) {
        case 6:
            *neighbors = (int*)neighbors_6;
            *num_neighbors = 6;
            break;
        case 18:
            *neighbors = (int*)neighbors_18;
            *num_neighbors = 18;
            break;
        case 26:
            *neighbors = (int*)neighbors_26;
            *num_neighbors = 26;
            break;
        default:
            *neighbors = (int*)neighbors_6;
            *num_neighbors = 6;
            break;
    }
}

/* Estimate maximum travel time for bucket sizing */
int estimate_max_travel_time(double *F, const mwSize *dims, double scale_factor) {
    /* Find minimum speed */
    double min_speed = F[0];
    for (int i = 1; i < dims[0]*dims[1]*dims[2]; i++) {
        if (F[i] < min_speed) min_speed = F[i];
    }
    min_speed = fmax(min_speed, EPS);
    
    /* Calculate maximum possible distance (diagonal) */
    double max_distance = sqrt(dims[0]*dims[0] + dims[1]*dims[1] + dims[2]*dims[2]);
    
    /* Estimate maximum travel time */
    double max_travel_time = max_distance / min_speed;
    int max_quantized_time = (int)(max_travel_time * scale_factor + 0.5);
    
    return fmin(max_quantized_time, MAX_BUCKETS - 1);
}

/* Main MEX function */
void mexFunction(int nlhs, mxArray *plhs[], int nrhs, const mxArray *prhs[]) {
    /* Input validation */
    if (nrhs < 2 || nrhs > 4) {
        mexErrMsgTxt("2 to 4 inputs required: F, SourcePoints, [connectivity], [scale_factor]");
    }
    if (nlhs != 1) {
        mexErrMsgTxt("One output required");
    }
    
    /* Check input types */
    if (mxGetClassID(prhs[0]) != mxDOUBLE_CLASS) {
        mexErrMsgTxt("Speed image must be of class double");
    }
    if (mxGetClassID(prhs[1]) != mxDOUBLE_CLASS) {
        mexErrMsgTxt("SourcePoints must be of class double");
    }
    
    /* Get input data */
    double *F = (double*)mxGetPr(prhs[0]);
    double *SourcePoints = (double*)mxGetPr(prhs[1]);
    
    /* Get optional parameters */
    int connectivity = (nrhs > 2) ? (int)mxGetScalar(prhs[2]) : 6;
    double scale_factor = (nrhs > 3) ? mxGetScalar(prhs[3]) : 1000.0;
    
    /* Get dimensions */
    const mwSize *dims = mxGetDimensions(prhs[0]);
    if (mxGetNumberOfDimensions(prhs[0]) != 3) {
        mexErrMsgTxt("Speed image must be 3D");
    }
    
    const mwSize *sp_dims = mxGetDimensions(prhs[1]);
    if (sp_dims[0] != 3) {
        mexErrMsgTxt("SourcePoints must be a 3xN matrix");
    }
    
    int npixels = dims[0] * dims[1] * dims[2];
    int num_sources = sp_dims[1];
    
    /* Create output array */
    plhs[0] = mxCreateNumericArray(3, dims, mxDOUBLE_CLASS, mxREAL);
    double *T = mxGetPr(plhs[0]);
    
    /* Initialize arrays */
    int *quantized_dist = (int*)malloc(npixels * sizeof(int));
    bool *visited = (bool*)calloc(npixels, sizeof(bool));
    
    /* Initialize distances */
    for (int i = 0; i < npixels; i++) {
        quantized_dist[i] = INT_MAX;
        T[i] = INF;
    }
    
    /* Estimate maximum travel time and create buckets */
    int max_buckets = estimate_max_travel_time(F, dims, scale_factor);
    max_buckets = fmin(max_buckets, MAX_BUCKETS);
    DialBuckets *buckets = dial_create_buckets(max_buckets);
    
    /* Initialize source points */
    for (int s = 0; s < num_sources; s++) {
        int sx = (int)(SourcePoints[0 + s*3] - 1);  /* Convert to 0-based */
        int sy = (int)(SourcePoints[1 + s*3] - 1);
        int sz = (int)(SourcePoints[2 + s*3] - 1);
        
        if (inbounds3d(sx, sy, sz, dims)) {
            int idx = mindex3(sx, sy, sz, dims[0], dims[1]);
            quantized_dist[idx] = 0;
            T[idx] = 0.0;
            dial_insert(buckets, 0, sx, sy, sz, idx);
        }
    }
    
    /* Get neighbor configuration */
    int *neighbors;
    int num_neighbors;
    get_neighbors(connectivity, &neighbors, &num_neighbors);
    
    /* Dial's algorithm main loop */
    DialNode *current;
    while ((current = dial_extract_min(buckets)) != NULL) {
        int current_idx = current->index;
        
        if (visited[current_idx]) {
            free(current);
            continue;
        }
        
        visited[current_idx] = true;
        
        /* Check all neighbors */
        for (int n = 0; n < num_neighbors; n++) {
            int nx = current->x + neighbors[n*3 + 0];
            int ny = current->y + neighbors[n*3 + 1];
            int nz = current->z + neighbors[n*3 + 2];
            
            if (!inbounds3d(nx, ny, nz, dims)) continue;
            
            int neighbor_idx = mindex3(nx, ny, nz, dims[0], dims[1]);
            if (visited[neighbor_idx]) continue;
            
            /* Calculate quantized travel time to neighbor */
            int travel_time = calculate_quantized_travel_time(F, dims, 
                                                            current->x, current->y, current->z,
                                                            nx, ny, nz, scale_factor);
            int new_distance = quantized_dist[current_idx] + travel_time;
            
            /* Update if shorter path found */
            if (new_distance < quantized_dist[neighbor_idx]) {
                quantized_dist[neighbor_idx] = new_distance;
                T[neighbor_idx] = (double)new_distance / scale_factor;
                
                /* Insert into appropriate bucket */
                int bucket_idx = new_distance - buckets->current_bucket;
                if (bucket_idx >= 0 && bucket_idx < buckets->num_buckets) {
                    dial_insert(buckets, bucket_idx, nx, ny, nz, neighbor_idx);
                }
            }
        }
        
        free(current);
    }
    
    /* Cleanup */
    dial_destroy_buckets(buckets);
    free(quantized_dist);
    free(visited);
}
