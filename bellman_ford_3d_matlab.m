function T = bellman_ford_3d_matlab(SpeedImage, SourcePoints, connectivity, max_iterations)
%BELLMAN_FORD_3D_MATLAB Bellman-Ford算法的3D实现
% 使用Bellman-Ford算法计算3D网格中的传播时间
% 能够处理负权重边并检测负环
%
% 输入:
%   SpeedImage - 3D速度场 [nx x ny x nz]
%   SourcePoints - 源点坐标 [3 x N] (1-based索引)
%   connectivity - 连接性: 6, 18, 或 26 (默认6)
%   max_iterations - 最大迭代次数 (默认V-1)
%
% 输出:
%   T - 传播时间场 [nx x ny x nz]

if nargin < 3, connectivity = 6; end
if nargin < 4, max_iterations = []; end

% 获取尺寸
[nx, ny, nz] = size(SpeedImage);
num_sources = size(SourcePoints, 2);
total_nodes = nx * ny * nz;

% 设置默认最大迭代次数
if isempty(max_iterations)
    max_iterations = total_nodes - 1;
end

fprintf('开始Bellman-Ford算法，网格大小: %dx%dx%d\n', nx, ny, nz);
fprintf('最大迭代次数: %d\n', max_iterations);

% 初始化距离
T = inf(nx, ny, nz);

% 初始化源点
for s = 1:num_sources
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    if sx >= 1 && sx <= nx && sy >= 1 && sy <= ny && sz >= 1 && sz <= nz
        T(sx, sy, sz) = 0;
    end
end

% 定义邻居偏移
neighbors = get_neighbor_offsets(connectivity);
num_neighbors = size(neighbors, 1);

% 构建边列表
fprintf('构建边列表...\n');
edges = build_edge_list(SpeedImage, neighbors);
num_edges = size(edges, 1);
fprintf('总边数: %d\n', num_edges);

% Bellman-Ford主循环
fprintf('开始Bellman-Ford迭代...\n');
tic;

for iteration = 1:max_iterations
    updated = false;
    updates_count = 0;
    
    % 松弛所有边
    for e = 1:num_edges
        from_x = edges(e, 1);
        from_y = edges(e, 2);
        from_z = edges(e, 3);
        to_x = edges(e, 4);
        to_y = edges(e, 5);
        to_z = edges(e, 6);
        weight = edges(e, 7);
        
        if T(from_x, from_y, from_z) ~= inf
            new_distance = T(from_x, from_y, from_z) + weight;
            
            if new_distance < T(to_x, to_y, to_z)
                T(to_x, to_y, to_z) = new_distance;
                updated = true;
                updates_count = updates_count + 1;
            end
        end
    end
    
    % 显示进度
    if mod(iteration, 10) == 0 || iteration == max_iterations
        elapsed = toc;
        rate = iteration / elapsed;
        fprintf('迭代 %d/%d, 更新 %d 个节点, 速度: %.1f 迭代/秒\n', ...
            iteration, max_iterations, updates_count, rate);
    end
    
    % 早期终止：如果没有更新则收敛
    if ~updated
        fprintf('算法在第 %d 次迭代收敛\n', iteration);
        break;
    end
end

total_time = toc;

% 检测负环
fprintf('检测负环...\n');
has_negative_cycle = false;

for e = 1:num_edges
    from_x = edges(e, 1);
    from_y = edges(e, 2);
    from_z = edges(e, 3);
    to_x = edges(e, 4);
    to_y = edges(e, 5);
    to_z = edges(e, 6);
    weight = edges(e, 7);
    
    if T(from_x, from_y, from_z) ~= inf
        new_distance = T(from_x, from_y, from_z) + weight;
        
        if new_distance < T(to_x, to_y, to_z)
            has_negative_cycle = true;
            break;
        end
    end
end

if has_negative_cycle
    warning('检测到负环！结果可能不准确');
else
    fprintf('✓ 未检测到负环\n');
end

fprintf('Bellman-Ford算法完成！\n');
fprintf('总用时: %.2f秒, 总迭代: %d, 平均速度: %.1f 迭代/秒\n', ...
    total_time, iteration, iteration/total_time);

% 统计结果
finite_nodes = sum(isfinite(T(:)));
fprintf('有限距离节点: %d/%d (%.1f%%)\n', ...
    finite_nodes, total_nodes, 100*finite_nodes/total_nodes);

end

function neighbors = get_neighbor_offsets(connectivity)
%GET_NEIGHBOR_OFFSETS 获取邻居偏移量

switch connectivity
    case 6  % 面邻居
        neighbors = [
            -1,  0,  0;   1,  0,  0;   0, -1,  0;
             0,  1,  0;   0,  0, -1;   0,  0,  1
        ];
        
    case 18  % 面+边邻居
        neighbors = [
            % 面邻居
            -1,  0,  0;  1,  0,  0;  0, -1,  0;  0,  1,  0;  0,  0, -1;  0,  0,  1;
            % 边邻居
            -1, -1,  0; -1,  1,  0;  1, -1,  0;  1,  1,  0;
            -1,  0, -1; -1,  0,  1;  1,  0, -1;  1,  0,  1;
             0, -1, -1;  0, -1,  1;  0,  1, -1;  0,  1,  1
        ];
        
    case 26  % 面+边+角邻居
        neighbors = [
            % 面邻居
            -1,  0,  0;  1,  0,  0;  0, -1,  0;  0,  1,  0;  0,  0, -1;  0,  0,  1;
            % 边邻居
            -1, -1,  0; -1,  1,  0;  1, -1,  0;  1,  1,  0;
            -1,  0, -1; -1,  0,  1;  1,  0, -1;  1,  0,  1;
             0, -1, -1;  0, -1,  1;  0,  1, -1;  0,  1,  1;
            % 角邻居
            -1, -1, -1; -1, -1,  1; -1,  1, -1; -1,  1,  1;
             1, -1, -1;  1, -1,  1;  1,  1, -1;  1,  1,  1
        ];
        
    otherwise
        error('连接性必须是 6, 18, 或 26');
end

end

function edges = build_edge_list(SpeedImage, neighbors)
%BUILD_EDGE_LIST 构建边列表
% 返回格式: [from_x, from_y, from_z, to_x, to_y, to_z, weight]

[nx, ny, nz] = size(SpeedImage);
num_neighbors = size(neighbors, 1);

% 预估边数
estimated_edges = nx * ny * nz * num_neighbors;
edges = zeros(estimated_edges, 7);
edge_count = 0;

for x = 1:nx
    for y = 1:ny
        for z = 1:nz
            % 检查所有邻居
            for n = 1:num_neighbors
                nx_coord = x + neighbors(n, 1);
                ny_coord = y + neighbors(n, 2);
                nz_coord = z + neighbors(n, 3);
                
                % 边界检查
                if nx_coord >= 1 && nx_coord <= nx && ...
                   ny_coord >= 1 && ny_coord <= ny && ...
                   nz_coord >= 1 && nz_coord <= nz
                    
                    % 计算边权重（传播时间）
                    weight = calculate_travel_time_bf(SpeedImage, ...
                        x, y, z, nx_coord, ny_coord, nz_coord);
                    
                    % 添加边
                    edge_count = edge_count + 1;
                    edges(edge_count, :) = [x, y, z, nx_coord, ny_coord, nz_coord, weight];
                end
            end
        end
    end
end

% 裁剪到实际大小
edges = edges(1:edge_count, :);

end

function travel_time = calculate_travel_time_bf(SpeedImage, x1, y1, z1, x2, y2, z2)
%CALCULATE_TRAVEL_TIME_BF 计算传播时间

% 获取两点的速度
v1 = SpeedImage(x1, y1, z1);
v2 = SpeedImage(x2, y2, z2);

% 使用调和平均速度
v_avg = 2 / (1/max(v1, 1e-8) + 1/max(v2, 1e-8));

% 计算欧几里得距离
dx = x2 - x1;
dy = y2 - y1;
dz = z2 - z1;
distance = sqrt(dx^2 + dy^2 + dz^2);

% 返回传播时间
travel_time = distance / v_avg;

end
