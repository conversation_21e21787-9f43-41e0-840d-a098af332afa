function T = astar_3d_matlab(SpeedImage, SourcePoints, connectivity, heuristic_weight)
%ASTAR_3D_MATLAB A*算法的3D实现
% 使用A*搜索算法计算3D网格中的传播时间，通过启发式函数提高效率
%
% 输入:
%   SpeedImage - 3D速度场 [nx x ny x nz]
%   SourcePoints - 源点坐标 [3 x N] (1-based索引)
%   connectivity - 连接性: 6, 18, 或 26 (默认6)
%   heuristic_weight - 启发式权重 (默认1.0)
%
% 输出:
%   T - 传播时间场 [nx x ny x nz]

if nargin < 3, connectivity = 6; end
if nargin < 4, heuristic_weight = 1.0; end

% 获取尺寸
[nx, ny, nz] = size(SpeedImage);
num_sources = size(SourcePoints, 2);

% 初始化
T = inf(nx, ny, nz);
g_cost = inf(nx, ny, nz);  % 实际代价
f_cost = inf(nx, ny, nz);  % 总代价 (g + h)
closed_set = false(nx, ny, nz);

% 计算平均速度用于启发式函数
avg_speed = mean(SpeedImage(:));

% 定义邻居偏移
neighbors = get_neighbor_offsets(connectivity);
num_neighbors = size(neighbors, 1);

% 开放集合 (优先队列)
% 格式: [f_cost, g_cost, x, y, z]
open_set = [];

% 初始化源点
for s = 1:num_sources
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    if sx >= 1 && sx <= nx && sy >= 1 && sy <= ny && sz >= 1 && sz <= nz
        g_cost(sx, sy, sz) = 0;
        h_cost = calculate_heuristic(sx, sy, sz, SourcePoints, avg_speed, heuristic_weight);
        f_cost(sx, sy, sz) = h_cost;
        T(sx, sy, sz) = 0;
        
        open_set = [open_set; [h_cost, 0, sx, sy, sz]];
    end
end

fprintf('开始A*算法，网格大小: %dx%dx%d，启发式权重: %.2f\n', nx, ny, nz, heuristic_weight);
processed_count = 0;
total_nodes = nx * ny * nz;

% A*主循环
tic;
while ~isempty(open_set)
    % 找到f_cost最小的节点
    [~, min_idx] = min(open_set(:, 1));
    current = open_set(min_idx, :);
    open_set(min_idx, :) = [];  % 从开放集合中移除
    
    cx = current(3);
    cy = current(4);
    cz = current(5);
    current_g = current(2);
    
    % 跳过已处理的节点
    if closed_set(cx, cy, cz)
        continue;
    end
    
    % 添加到关闭集合
    closed_set(cx, cy, cz) = true;
    processed_count = processed_count + 1;
    
    % 显示进度
    if mod(processed_count, 20000) == 0
        elapsed = toc;
        rate = processed_count / elapsed;
        remaining = (total_nodes - processed_count) / rate;
        fprintf('进度: %d/%d (%.1f%%), 速度: %.0f节点/秒, 预计剩余: %.1f秒\n', ...
            processed_count, total_nodes, 100*processed_count/total_nodes, rate, remaining);
    end
    
    % 检查所有邻居
    for n = 1:num_neighbors
        nx_coord = cx + neighbors(n, 1);
        ny_coord = cy + neighbors(n, 2);
        nz_coord = cz + neighbors(n, 3);
        
        % 边界检查
        if nx_coord < 1 || nx_coord > nx || ...
           ny_coord < 1 || ny_coord > ny || ...
           nz_coord < 1 || nz_coord > nz
            continue;
        end
        
        % 跳过已关闭的邻居
        if closed_set(nx_coord, ny_coord, nz_coord)
            continue;
        end
        
        % 计算到邻居的传播时间
        travel_time = calculate_travel_time_astar(SpeedImage, ...
            cx, cy, cz, nx_coord, ny_coord, nz_coord);
        tentative_g = current_g + travel_time;
        
        % 如果找到更好的路径
        if tentative_g < g_cost(nx_coord, ny_coord, nz_coord)
            g_cost(nx_coord, ny_coord, nz_coord) = tentative_g;
            
            % 计算启发式代价
            h_cost = calculate_heuristic(nx_coord, ny_coord, nz_coord, ...
                SourcePoints, avg_speed, heuristic_weight);
            f_total = tentative_g + h_cost;
            f_cost(nx_coord, ny_coord, nz_coord) = f_total;
            
            T(nx_coord, ny_coord, nz_coord) = tentative_g;
            
            % 添加到开放集合
            open_set = [open_set; [f_total, tentative_g, nx_coord, ny_coord, nz_coord]];
        end
    end
end

total_time = toc;
fprintf('A*算法完成！\n');
fprintf('总用时: %.2f秒, 处理节点: %d, 平均速度: %.0f节点/秒\n', ...
    total_time, processed_count, processed_count/total_time);

end

function neighbors = get_neighbor_offsets(connectivity)
%GET_NEIGHBOR_OFFSETS 获取邻居偏移量

switch connectivity
    case 6  % 面邻居
        neighbors = [
            -1,  0,  0;   1,  0,  0;   0, -1,  0;
             0,  1,  0;   0,  0, -1;   0,  0,  1
        ];
        
    case 18  % 面+边邻居
        neighbors = [
            % 面邻居
            -1,  0,  0;  1,  0,  0;  0, -1,  0;  0,  1,  0;  0,  0, -1;  0,  0,  1;
            % 边邻居
            -1, -1,  0; -1,  1,  0;  1, -1,  0;  1,  1,  0;
            -1,  0, -1; -1,  0,  1;  1,  0, -1;  1,  0,  1;
             0, -1, -1;  0, -1,  1;  0,  1, -1;  0,  1,  1
        ];
        
    case 26  % 面+边+角邻居
        neighbors = [
            % 面邻居
            -1,  0,  0;  1,  0,  0;  0, -1,  0;  0,  1,  0;  0,  0, -1;  0,  0,  1;
            % 边邻居
            -1, -1,  0; -1,  1,  0;  1, -1,  0;  1,  1,  0;
            -1,  0, -1; -1,  0,  1;  1,  0, -1;  1,  0,  1;
             0, -1, -1;  0, -1,  1;  0,  1, -1;  0,  1,  1;
            % 角邻居
            -1, -1, -1; -1, -1,  1; -1,  1, -1; -1,  1,  1;
             1, -1, -1;  1, -1,  1;  1,  1, -1;  1,  1,  1
        ];
        
    otherwise
        error('连接性必须是 6, 18, 或 26');
end

end

function travel_time = calculate_travel_time_astar(SpeedImage, x1, y1, z1, x2, y2, z2)
%CALCULATE_TRAVEL_TIME_ASTAR 计算两个相邻体素间的传播时间

% 获取两点的速度
v1 = SpeedImage(x1, y1, z1);
v2 = SpeedImage(x2, y2, z2);

% 使用调和平均速度
v_avg = 2 / (1/max(v1, 1e-8) + 1/max(v2, 1e-8));

% 计算欧几里得距离
dx = x2 - x1;
dy = y2 - y1;
dz = z2 - z1;
distance = sqrt(dx^2 + dy^2 + dz^2);

% 返回传播时间
travel_time = distance / v_avg;

end

function h_cost = calculate_heuristic(x, y, z, SourcePoints, avg_speed, weight)
%CALCULATE_HEURISTIC 计算启发式代价
% 使用到最近源点的欧几里得距离作为启发式

num_sources = size(SourcePoints, 2);
min_distance = inf;

% 找到到最近源点的距离
for s = 1:num_sources
    sx = SourcePoints(1, s);
    sy = SourcePoints(2, s);
    sz = SourcePoints(3, s);
    
    dx = x - sx;
    dy = y - sy;
    dz = z - sz;
    distance = sqrt(dx^2 + dy^2 + dz^2);
    
    if distance < min_distance
        min_distance = distance;
    end
end

% 启发式代价 = 权重 * 距离 / 平均速度
h_cost = weight * min_distance / avg_speed;

end
