%% 几何分析法与MSFM3D完整比较测试
% 复现您原始测试的功能，并添加几何分析法比较

clear; clc;

fprintf('=== 几何分析法与MSFM3D方法比较 ===\n\n');

%% 设置测试参数（与您的原始代码相同）
SourcePoint = [1; 1; 1];
SpeedImage = ones([200 200 200]) * 1500;

% 创建坐标网格
[X, Y, Z] = ndgrid(1:200, 1:200, 1:200);

% 理论解（真值）
T1 = (sqrt((X-SourcePoint(1)).^2 + (Y-SourcePoint(2)).^2 + (Z-SourcePoint(3)).^2)) ./ SpeedImage;

%% 方法1: 几何分析法（直接计算）
fprintf('1. 运行几何分析法（直接几何计算）...\n');
tic;
T1_GEOMETRIC = geometric_analysis_simple(SpeedImage, SourcePoint);
time_geometric = toc;
fprintf('   用时: %.4f 秒\n', time_geometric);

%% 方法2-5: MSFM3D的四种配置（如您的原始代码）
fprintf('2. 运行MSFM3D - FMM1 (一阶，无交叉邻居)...\n');
tic; 
try
    T1_FMM1 = msfm3d(SpeedImage, SourcePoint, false, false); 
    time_fmm1 = toc;
    fprintf('   用时: %.4f 秒\n', time_fmm1);
catch ME
    fprintf('   错误: %s\n', ME.message);
    T1_FMM1 = [];
    time_fmm1 = NaN;
end

fprintf('3. 运行MSFM3D - MSFM1 (一阶，有交叉邻居)...\n');
tic; 
try
    T1_MSFM1 = msfm3d(SpeedImage, SourcePoint, false, true); 
    time_msfm1 = toc;
    fprintf('   用时: %.4f 秒\n', time_msfm1);
catch ME
    fprintf('   错误: %s\n', ME.message);
    T1_MSFM1 = [];
    time_msfm1 = NaN;
end

fprintf('4. 运行MSFM3D - FMM2 (二阶，无交叉邻居)...\n');
tic; 
try
    T1_FMM2 = msfm3d(SpeedImage, SourcePoint, true, false); 
    time_fmm2 = toc;
    fprintf('   用时: %.4f 秒\n', time_fmm2);
catch ME
    fprintf('   错误: %s\n', ME.message);
    T1_FMM2 = [];
    time_fmm2 = NaN;
end

fprintf('5. 运行MSFM3D - MSFM2 (二阶，有交叉邻居)...\n');
tic; 
try
    T1_MSFM2 = msfm3d(SpeedImage, SourcePoint, true, true); 
    time_msfm2 = toc;
    fprintf('   用时: %.4f 秒\n', time_msfm2);
catch ME
    fprintf('   错误: %s\n', ME.message);
    T1_MSFM2 = [];
    time_msfm2 = NaN;
end

%% 计算误差并显示结果
fprintf('\n=== 精度比较结果 ===\n');
fprintf('方法      L1误差    L2误差    L∞误差    计算时间(秒)\n');
fprintf('----------------------------------------------------\n');

% 几何分析法结果
if ~isempty(T1_GEOMETRIC)
    errors_geo = calculate_errors(T1, T1_GEOMETRIC);
    fprintf('几何法:   %9.5f %9.5f %9.5f %9.4f\n', ...
        errors_geo.L1, errors_geo.L2, errors_geo.Linf, time_geometric);
end

% MSFM方法结果
methods = {'FMM1', 'MSFM1', 'FMM2', 'MSFM2'};
results = {T1_FMM1, T1_MSFM1, T1_FMM2, T1_MSFM2};
times = [time_fmm1, time_msfm1, time_fmm2, time_msfm2];

for i = 1:length(methods)
    if ~isempty(results{i})
        errors = calculate_errors(T1, results{i});
        fprintf('%-8s: %9.5f %9.5f %9.5f %9.4f\n', ...
            methods{i}, errors.L1, errors.L2, errors.Linf, times(i));
    else
        fprintf('%-8s: %9s %9s %9s %9s\n', methods{i}, 'N/A', 'N/A', 'N/A', 'N/A');
    end
end

%% 可视化比较（选择中间切片）
if ~isempty(T1_GEOMETRIC)
    slice_idx = 100;  % 中间切片
    
    figure('Position', [100, 100, 1500, 600]);
    
    % 理论解
    subplot(2, 3, 1);
    imagesc(squeeze(T1(:, :, slice_idx)));
    colorbar; title('理论解'); axis equal tight;
    
    % 几何分析法
    subplot(2, 3, 2);
    imagesc(squeeze(T1_GEOMETRIC(:, :, slice_idx)));
    colorbar; title('几何分析法'); axis equal tight;
    
    % MSFM方法（如果可用）
    plot_idx = 3;
    for i = 1:length(methods)
        if ~isempty(results{i}) && plot_idx <= 6
            subplot(2, 3, plot_idx);
            imagesc(squeeze(results{i}(:, :, slice_idx)));
            colorbar; title(methods{i}); axis equal tight;
            plot_idx = plot_idx + 1;
        end
    end
    
    sgtitle(sprintf('传播时间比较 (Z=%d切片)', slice_idx));
end

%% 误差分布分析
if ~isempty(T1_GEOMETRIC)
    figure('Position', [200, 200, 1200, 400]);
    
    % 几何分析法误差分布
    subplot(1, 3, 1);
    error_geo = T1 - T1_GEOMETRIC;
    histogram(error_geo(:), 50);
    title('几何分析法误差分布');
    xlabel('误差'); ylabel('频次');
    
    % 如果MSFM2可用，显示其误差分布
    if ~isempty(T1_MSFM2)
        subplot(1, 3, 2);
        error_msfm2 = T1 - T1_MSFM2;
        histogram(error_msfm2(:), 50);
        title('MSFM2误差分布');
        xlabel('误差'); ylabel('频次');
        
        % 两种方法的差异
        subplot(1, 3, 3);
        diff_methods = T1_GEOMETRIC - T1_MSFM2;
        histogram(diff_methods(:), 50);
        title('几何法与MSFM2差异');
        xlabel('差异'); ylabel('频次');
    end
end

%% 性能总结
fprintf('\n=== 性能总结 ===\n');
fprintf('1. 精度排序（L1误差从小到大）:\n');

% 收集所有有效结果
all_methods = {'几何法'};
all_errors = [];
all_times = [];

if ~isempty(T1_GEOMETRIC)
    errors_geo = calculate_errors(T1, T1_GEOMETRIC);
    all_errors = [all_errors, errors_geo.L1];
    all_times = [all_times, time_geometric];
end

for i = 1:length(methods)
    if ~isempty(results{i})
        all_methods{end+1} = methods{i};
        errors = calculate_errors(T1, results{i});
        all_errors = [all_errors, errors.L1];
        all_times = [all_times, times(i)];
    end
end

% 按精度排序
if ~isempty(all_errors)
    [sorted_errors, sort_idx] = sort(all_errors);
    for i = 1:length(sort_idx)
        fprintf('   %d. %s: L1=%.6f, 时间=%.4fs\n', ...
            i, all_methods{sort_idx(i)}, sorted_errors(i), all_times(sort_idx(i)));
    end
end

fprintf('\n2. 速度排序（计算时间从小到大）:\n');
if ~isempty(all_times)
    [sorted_times, sort_idx] = sort(all_times);
    for i = 1:length(sort_idx)
        fprintf('   %d. %s: 时间=%.4fs, L1=%.6f\n', ...
            i, all_methods{sort_idx(i)}, sorted_times(i), all_errors(sort_idx(i)));
    end
end

fprintf('\n3. 方法特点:\n');
fprintf('   - 几何分析法: 在均匀介质中理论上最精确，计算速度快\n');
fprintf('   - MSFM方法: 适用于复杂介质，精度随阶数和模板增加而提高\n');

%% 辅助函数
function errors = calculate_errors(reference, computed)
    % 计算L1, L2, L∞误差
    diff = reference(:) - computed(:);
    errors.L1 = mean(abs(diff));
    errors.L2 = sqrt(mean(diff.^2));
    errors.Linf = max(abs(diff));
end
