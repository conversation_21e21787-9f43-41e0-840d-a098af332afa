#include "mex.h"
#include "math.h"
#include <stdlib.h>
#include <stdbool.h>

/*
 * <PERSON>jkstra Algorithm for 3D Distance Calculation
 * This function calculates travel times using <PERSON>jk<PERSON>'s shortest path algorithm
 * treating the 3D grid as a graph where each voxel is a node.
 * 
 * T = dijkstra_3d(F, SourcePoints, connectivity)
 * 
 * Inputs:
 *   F: The 3D speed/velocity image
 *   SourcePoints: A list of starting points [3 x N]
 *   connectivity: 6 (face neighbors), 18 (face+edge), or 26 (face+edge+corner)
 * 
 * Outputs:
 *   T: Travel time image from SourcePoints to all pixels
 */

#define INF 1e10
#define EPS 1e-8

/* Priority queue node structure */
typedef struct {
    int x, y, z;
    double distance;
    int index;
} PQNode;

/* Priority queue structure */
typedef struct {
    PQNode *nodes;
    int size;
    int capacity;
} PriorityQueue;

/* Helper function to calculate 3D index */
int mindex3(int x, int y, int z, int sizx, int sizy) {
    return z*sizx*sizy + y*sizx + x;
}

/* Check if coordinates are within bounds */
bool inbounds3d(int x, int y, int z, const mwSize *dims) {
    return (x >= 0 && x < dims[0] && y >= 0 && y < dims[1] && z >= 0 && z < dims[2]);
}

/* Initialize priority queue */
PriorityQueue* pq_create(int capacity) {
    PriorityQueue *pq = (PriorityQueue*)malloc(sizeof(PriorityQueue));
    pq->nodes = (PQNode*)malloc(capacity * sizeof(PQNode));
    pq->size = 0;
    pq->capacity = capacity;
    return pq;
}

/* Destroy priority queue */
void pq_destroy(PriorityQueue *pq) {
    free(pq->nodes);
    free(pq);
}

/* Swap two nodes in priority queue */
void pq_swap(PriorityQueue *pq, int i, int j) {
    PQNode temp = pq->nodes[i];
    pq->nodes[i] = pq->nodes[j];
    pq->nodes[j] = temp;
}

/* Heapify up */
void pq_heapify_up(PriorityQueue *pq, int index) {
    while (index > 0) {
        int parent = (index - 1) / 2;
        if (pq->nodes[index].distance >= pq->nodes[parent].distance) break;
        pq_swap(pq, index, parent);
        index = parent;
    }
}

/* Heapify down */
void pq_heapify_down(PriorityQueue *pq, int index) {
    while (true) {
        int smallest = index;
        int left = 2 * index + 1;
        int right = 2 * index + 2;
        
        if (left < pq->size && pq->nodes[left].distance < pq->nodes[smallest].distance)
            smallest = left;
        if (right < pq->size && pq->nodes[right].distance < pq->nodes[smallest].distance)
            smallest = right;
            
        if (smallest == index) break;
        pq_swap(pq, index, smallest);
        index = smallest;
    }
}

/* Insert node into priority queue */
void pq_insert(PriorityQueue *pq, int x, int y, int z, double distance, int index) {
    if (pq->size >= pq->capacity) {
        /* Resize if needed */
        pq->capacity *= 2;
        pq->nodes = (PQNode*)realloc(pq->nodes, pq->capacity * sizeof(PQNode));
    }
    
    pq->nodes[pq->size].x = x;
    pq->nodes[pq->size].y = y;
    pq->nodes[pq->size].z = z;
    pq->nodes[pq->size].distance = distance;
    pq->nodes[pq->size].index = index;
    
    pq_heapify_up(pq, pq->size);
    pq->size++;
}

/* Extract minimum from priority queue */
PQNode pq_extract_min(PriorityQueue *pq) {
    PQNode min_node = pq->nodes[0];
    pq->nodes[0] = pq->nodes[pq->size - 1];
    pq->size--;
    if (pq->size > 0) {
        pq_heapify_down(pq, 0);
    }
    return min_node;
}

/* Check if priority queue is empty */
bool pq_is_empty(PriorityQueue *pq) {
    return pq->size == 0;
}

/* Calculate travel time between two adjacent voxels */
double calculate_travel_time(double *F, const mwSize *dims, 
                           int x1, int y1, int z1, 
                           int x2, int y2, int z2) {
    /* Get velocities at both points */
    double v1 = F[mindex3(x1, y1, z1, dims[0], dims[1])];
    double v2 = F[mindex3(x2, y2, z2, dims[0], dims[1])];
    
    /* Use harmonic mean of velocities */
    double v_avg = 2.0 / (1.0/fmax(v1, EPS) + 1.0/fmax(v2, EPS));
    
    /* Calculate Euclidean distance */
    double dx = x2 - x1;
    double dy = y2 - y1;
    double dz = z2 - z1;
    double distance = sqrt(dx*dx + dy*dy + dz*dz);
    
    return distance / v_avg;
}

/* Get neighbor offsets based on connectivity */
void get_neighbors(int connectivity, int **neighbors, int *num_neighbors) {
    static int neighbors_6[6][3] = {
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1}
    };
    
    static int neighbors_18[18][3] = {
        /* Face neighbors */
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1},
        /* Edge neighbors */
        {-1, -1, 0}, {-1, 1, 0}, {1, -1, 0}, {1, 1, 0},
        {-1, 0, -1}, {-1, 0, 1}, {1, 0, -1}, {1, 0, 1},
        {0, -1, -1}, {0, -1, 1}, {0, 1, -1}, {0, 1, 1}
    };
    
    static int neighbors_26[26][3] = {
        /* Face neighbors */
        {-1, 0, 0}, {1, 0, 0}, {0, -1, 0}, {0, 1, 0}, {0, 0, -1}, {0, 0, 1},
        /* Edge neighbors */
        {-1, -1, 0}, {-1, 1, 0}, {1, -1, 0}, {1, 1, 0},
        {-1, 0, -1}, {-1, 0, 1}, {1, 0, -1}, {1, 0, 1},
        {0, -1, -1}, {0, -1, 1}, {0, 1, -1}, {0, 1, 1},
        /* Corner neighbors */
        {-1, -1, -1}, {-1, -1, 1}, {-1, 1, -1}, {-1, 1, 1},
        {1, -1, -1}, {1, -1, 1}, {1, 1, -1}, {1, 1, 1}
    };
    
    switch (connectivity) {
        case 6:
            *neighbors = (int*)neighbors_6;
            *num_neighbors = 6;
            break;
        case 18:
            *neighbors = (int*)neighbors_18;
            *num_neighbors = 18;
            break;
        case 26:
            *neighbors = (int*)neighbors_26;
            *num_neighbors = 26;
            break;
        default:
            *neighbors = (int*)neighbors_6;
            *num_neighbors = 6;
            break;
    }
}

/* Main MEX function */
void mexFunction(int nlhs, mxArray *plhs[], int nrhs, const mxArray *prhs[]) {
    /* Input validation */
    if (nrhs < 2 || nrhs > 3) {
        mexErrMsgTxt("2 or 3 inputs required: F, SourcePoints, [connectivity]");
    }
    if (nlhs != 1) {
        mexErrMsgTxt("One output required");
    }
    
    /* Check input types */
    if (mxGetClassID(prhs[0]) != mxDOUBLE_CLASS) {
        mexErrMsgTxt("Speed image must be of class double");
    }
    if (mxGetClassID(prhs[1]) != mxDOUBLE_CLASS) {
        mexErrMsgTxt("SourcePoints must be of class double");
    }
    
    /* Get input data */
    double *F = (double*)mxGetPr(prhs[0]);
    double *SourcePoints = (double*)mxGetPr(prhs[1]);
    
    /* Get connectivity (default = 6) */
    int connectivity = 6;
    if (nrhs > 2) {
        connectivity = (int)mxGetScalar(prhs[2]);
    }
    
    /* Get dimensions */
    const mwSize *dims = mxGetDimensions(prhs[0]);
    if (mxGetNumberOfDimensions(prhs[0]) != 3) {
        mexErrMsgTxt("Speed image must be 3D");
    }
    
    const mwSize *sp_dims = mxGetDimensions(prhs[1]);
    if (sp_dims[0] != 3) {
        mexErrMsgTxt("SourcePoints must be a 3xN matrix");
    }
    
    int npixels = dims[0] * dims[1] * dims[2];
    int num_sources = sp_dims[1];
    
    /* Create output array */
    plhs[0] = mxCreateNumericArray(3, dims, mxDOUBLE_CLASS, mxREAL);
    double *T = mxGetPr(plhs[0]);
    
    /* Initialize distance array */
    bool *visited = (bool*)calloc(npixels, sizeof(bool));
    
    /* Initialize all distances to infinity */
    for (int i = 0; i < npixels; i++) {
        T[i] = INF;
    }
    
    /* Create priority queue */
    PriorityQueue *pq = pq_create(npixels);
    
    /* Initialize source points */
    for (int s = 0; s < num_sources; s++) {
        int sx = (int)(SourcePoints[0 + s*3] - 1);  /* Convert to 0-based */
        int sy = (int)(SourcePoints[1 + s*3] - 1);
        int sz = (int)(SourcePoints[2 + s*3] - 1);
        
        if (inbounds3d(sx, sy, sz, dims)) {
            int idx = mindex3(sx, sy, sz, dims[0], dims[1]);
            T[idx] = 0.0;
            pq_insert(pq, sx, sy, sz, 0.0, idx);
        }
    }
    
    /* Get neighbor configuration */
    int *neighbors;
    int num_neighbors;
    get_neighbors(connectivity, &neighbors, &num_neighbors);
    
    /* Dijkstra's algorithm main loop */
    while (!pq_is_empty(pq)) {
        PQNode current = pq_extract_min(pq);
        
        if (visited[current.index]) continue;
        visited[current.index] = true;
        
        /* Check all neighbors */
        for (int n = 0; n < num_neighbors; n++) {
            int nx = current.x + neighbors[n*3 + 0];
            int ny = current.y + neighbors[n*3 + 1];
            int nz = current.z + neighbors[n*3 + 2];
            
            if (!inbounds3d(nx, ny, nz, dims)) continue;
            
            int neighbor_idx = mindex3(nx, ny, nz, dims[0], dims[1]);
            if (visited[neighbor_idx]) continue;
            
            /* Calculate travel time to neighbor */
            double travel_time = calculate_travel_time(F, dims, 
                                                     current.x, current.y, current.z,
                                                     nx, ny, nz);
            double new_distance = current.distance + travel_time;
            
            /* Update if shorter path found */
            if (new_distance < T[neighbor_idx]) {
                T[neighbor_idx] = new_distance;
                pq_insert(pq, nx, ny, nz, new_distance, neighbor_idx);
            }
        }
    }
    
    /* Cleanup */
    pq_destroy(pq);
    free(visited);
}
